# Kina Restaurante - Sistema de Gerenciamento de Produtos

Este sistema permite gerenciar os produtos do Kina Restaurante, incluindo a atualização dos IDs dos produtos no iFood e a sincronização desses IDs com a landing page.

## Estrutura do Projeto

- `kina_database_model.sql`: Script SQL para criar o banco de dados e tabelas
- `kina_product_manager.php`: Interface para gerenciar produtos e seus IDs no iFood
- `update_landing_page_links.php`: Script para atualizar os links na landing page
- `kina-landing-page.html`: Landing page do Kina Restaurante
- `index.html`: Landing page alternativa moderna e responsiva

## Tecnologias Utilizadas

- HTML5
- CSS3
- JavaScript
- PHP
- MySQL

## Configuração Inicial

### 1. Criar o Banco de Dados

1. Acesse o MySQL através do phpMyAdmin ou linha de comando
2. Execute o script `kina_database_model.sql` para criar o banco de dados e tabelas
3. O script também insere dados iniciais para unidades, categorias, produtos, benefícios e FAQ

### 2. Configurar Conexão com o Banco de Dados

Edite os arquivos `kina_product_manager.php` e `update_landing_page_links.php` para configurar a conexão com o banco de dados:

```php
$config = [
    'host' => 'localhost',     // Endereço do servidor MySQL
    'dbname' => 'kina_restaurante', // Nome do banco de dados
    'username' => 'seu_usuario',    // Nome de usuário do MySQL
    'password' => 'sua_senha',      // Senha do MySQL
    'charset' => 'utf8mb4'
];
```

## Como Usar

### Gerenciador de Produtos

O arquivo `kina_product_manager.php` fornece uma interface para gerenciar os produtos e seus IDs no iFood:

1. Acesse o arquivo através do navegador (ex: `http://localhost/kina_product_manager.php`)
2. Use o formulário para atualizar o ID do iFood para cada produto
3. O ID do iFood é a parte da URL após o parâmetro `?prato=`
   - Exemplo: Em `https://www.ifood.com.br/delivery/fortaleza-ce/kina-del-paseo-aldeota/053acccc-102c-4b47-b6df-b3ddc8d6c0f6?prato=20174c4a-7796-4eb3-b3bd-6037aa1fcab5`
   - O ID do produto é `20174c4a-7796-4eb3-b3bd-6037aa1fcab5`

### Atualizador de Links da Landing Page

O arquivo `update_landing_page_links.php` atualiza automaticamente os links dos produtos na landing page:

1. Acesse o arquivo através do navegador (ex: `http://localhost/update_landing_page_links.php`)
2. O script irá:
   - Buscar os produtos em destaque no banco de dados
   - Atualizar os links na landing page com os IDs corretos do iFood
   - Criar um backup da landing page original
   - Exibir um relatório dos produtos atualizados

## Como Obter os IDs Reais dos Produtos no iFood

Para obter os IDs reais dos produtos no iFood:

1. Acesse o site do iFood: `https://www.ifood.com.br/`
2. Pesquise pelo restaurante "Kina Iguatemi" em Fortaleza
3. Navegue até o produto desejado
4. Observe a URL no navegador após clicar no produto
5. O ID do produto é a parte após `?prato=`

Exemplo:
```
https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=20174c4a-7796-4eb3-b3bd-6037aa1fcab5
```

Neste exemplo, o ID do produto é `20174c4a-7796-4eb3-b3bd-6037aa1fcab5`.

## Modelo de Dados

O banco de dados inclui as seguintes tabelas:

1. `unidades`: Filiais do Kina Restaurante
2. `categorias`: Categorias de produtos (Entradas, Temakis, Combinados, etc.)
3. `produtos`: Produtos do restaurante
4. `produtos_unidades`: Relação entre produtos e unidades (preços e IDs específicos)
5. `beneficios`: Benefícios exibidos na seção "Por que pedir no Kina?"
6. `faq`: Perguntas frequentes
7. `configuracoes`: Configurações gerais do site

## Requisitos do Sistema

- PHP 7.4 ou superior
- MySQL 5.7 ou superior
- Servidor web (Apache, Nginx, etc.)
- Extensão PDO para PHP

## Notas Importantes

- Os IDs dos produtos no iFood são fictícios até serem atualizados com os valores reais
- Sempre faça backup da landing page antes de atualizar os links
- O sistema cria automaticamente backups da landing page antes de cada atualização

## Suporte

Para suporte ou dúvidas, entre em contato com o desenvolvedor.
