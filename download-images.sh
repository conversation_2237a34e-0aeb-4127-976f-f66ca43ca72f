#!/bin/bash

# Script para baixar imagens de exemplo para o carrossel

# Criar diretório de imagens se não existir
mkdir -p images

# Baixar imagens de exemplo
curl -o images/temaki-especial.jpg https://img.freepik.com/free-photo/temaki-sushi-with-salmon-cucumber-cream-cheese_661915-1.jpg
curl -o images/yakisoba-carne.jpg https://img.freepik.com/free-photo/stir-fried-noodles-with-beef-vegetables_661915-1.jpg
curl -o images/combinado-30.jpg https://img.freepik.com/free-photo/sushi-set-with-avocado-salmon-crab_661915-1.jpg
curl -o images/hot-roll.jpg https://img.freepik.com/free-photo/fried-sushi-rolls-with-salmon_661915-1.jpg
curl -o images/sashimi-salmao.jpg https://img.freepik.com/free-photo/salmon-sashimi-with-wasabi-ginger_661915-1.jpg
curl -o images/uramaki-philadelphia.jpg https://img.freepik.com/free-photo/philadelphia-roll-with-salmon-cream-cheese-cucumber_661915-1.jpg
curl -o images/gyoza.jpg https://img.freepik.com/free-photo/gyoza-dumplings-with-soy-sauce_661915-1.jpg
curl -o images/tempura-camarao.jpg https://img.freepik.com/free-photo/tempura-shrimp-with-sweet-chili-sauce_661915-1.jpg
curl -o images/hossomaki-kani.jpg https://img.freepik.com/free-photo/kani-maki-sushi-with-crab-meat_661915-1.jpg
curl -o images/niguiri-salmao.jpg https://img.freepik.com/free-photo/nigiri-sushi-with-salmon_661915-1.jpg

echo "Imagens baixadas com sucesso!"
