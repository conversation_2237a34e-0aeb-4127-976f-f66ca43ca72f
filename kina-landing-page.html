<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kina Restaurante - Delivery de Comida Japonesa em Fortaleza</title>
    <style>
        /* Variáveis de cores baseadas na identidade visual do Kina */
        :root {
            --primary-color: #ffcc00; /* Amarelo */
            --secondary-color: #e60000; /* Vermelho */
            --dark-color: #000000; /* Preto */
            --light-color: #ffffff; /* Branco */
            --gray-color: #f5f5f5; /* Cinza claro para fundos */
            --text-color: #333333;
            --font-primary: 'Montserrat', sans-serif;
            --font-secondary: 'Roboto', sans-serif;
        }

        /* Reset e estilos gerais */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--light-color);
            overflow-x: hidden; /* Previne scroll horizontal */
        }

        img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        p {
            margin-bottom: 1rem; /* Espaçamento consistente entre parágrafos */
        }

        h1, h2, h3, h4, h5, h6 {
            margin-top: 0;
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px; /* Aumentado para melhor espaçamento lateral */
        }

        .section {
            padding: 70px 0; /* Aumentado para melhor separação entre seções */
            margin-bottom: 0;
            position: relative;
        }

        .section-title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
            color: var(--dark-color);
            position: relative;
        }

        .section-title:after {
            content: '';
            display: block;
            width: 60px;
            height: 3px;
            background-color: var(--primary-color);
            margin: 10px auto;
        }

        /* Botão CTA principal */
        .btn-cta {
            display: inline-block;
            padding: 15px 30px;
            background-color: var(--secondary-color);
            color: var(--light-color);
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            box-shadow: 0 4px 10px rgba(230, 0, 0, 0.3);
        }

        .btn-cta:hover {
            background-color: #cc0000;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(230, 0, 0, 0.4);
        }

        .btn-cta-secondary {
            background-color: var(--primary-color);
            color: var(--dark-color);
            box-shadow: 0 4px 10px rgba(255, 204, 0, 0.3);
        }

        .btn-cta-secondary:hover {
            background-color: #e6b800;
            box-shadow: 0 6px 15px rgba(255, 204, 0, 0.4);
        }

        /* Header fixo */
        .header {
            background-color: var(--light-color);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 36px; /* Ajustado para ficar abaixo da barra de anúncio */
            left: 0;
            width: 100%;
            z-index: 1000;
            padding: 35px 0; /* Aumentado para 35px (20px a mais para cima e para baixo) */
            height: 120px; /* Aumentado para acomodar o padding maior */
        }

        /* Estilos para a barra de anúncio deslizante */
        .top-announcement-bar {
            background-color: var(--primary-color); /* Amarelo do Kina */
            color: var(--dark-color); /* Texto preto */
            width: 100%;
            text-align: center;
            padding: 8px 0;
            font-size: 14px;
            font-family: var(--font-primary);
            position: relative;
            z-index: 2000;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .announcement-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
        }

        .announcement-slider {
            display: flex;
            width: 300%;
            animation: slide 20s linear infinite;
        }

        .announcement-slide {
            width: 100%;
            flex-shrink: 0;
        }

        @keyframes slide {
            0% { transform: translateX(0); }
            28% { transform: translateX(0); }
            33% { transform: translateX(-33.33%); }
            61% { transform: translateX(-33.33%); }
            66% { transform: translateX(-66.66%); }
            94% { transform: translateX(-66.66%); }
            100% { transform: translateX(0); }
        }

        .top-announcement-bar p {
            margin: 0;
            font-weight: 600;
            letter-spacing: 0.2px;
            white-space: nowrap;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 100%;
        }

        .logo-container {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .logo {
            width: 120px;
            height: auto;
            object-fit: contain;
        }

        .header-cta {
            padding: 10px 25px; /* Aumentado para melhor visibilidade */
            font-size: 0.95rem;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        /* Hero Section */
        .hero {
            background-color: var(--dark-color);
            color: var(--light-color);
            padding: 210px 0 80px; /* Ajustado para compensar a barra de anúncio */
            text-align: center;
            background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://via.placeholder.com/1920x1080'); /* Substituir com imagem de fundo de comida japonesa */
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .hero-title {
            font-size: 2.8rem; /* Aumentado para maior impacto */
            margin-bottom: 20px;
            font-weight: 800;
            line-height: 1.2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* Sombra para melhorar legibilidade */
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 35px;
            font-weight: 300;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.5;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* Carrossel de produtos */
        .carousel-wrapper {
            position: relative;
            margin: 50px 0 30px;
            width: 100%;
            overflow: hidden;
        }

        .carousel-controls {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            transform: translateY(-50%);
            display: flex;
            justify-content: space-between;
            z-index: 10;
            padding: 0 10px;
        }

        .carousel-control {
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            color: var(--dark-color);
            border: none;
            border-radius: 50%;
            font-size: 1.2rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            opacity: 0.8;
        }

        .carousel-control:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        .carousel {
            overflow-x: auto;
            white-space: nowrap;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            padding-bottom: 15px;
            position: relative;
            scroll-behavior: smooth;
        }

        .carousel::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .carousel-container {
            display: inline-flex;
            gap: 25px; /* Aumentado para melhor separação entre itens */
            padding: 15px 10px;
            transition: transform 0.5s ease;
        }

        .carousel-item {
            width: 280px; /* Aumentado para melhor visualização */
            background-color: var(--light-color);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            display: inline-block;
            white-space: normal;
            border: 1px solid rgba(0, 0, 0, 0.05);
            flex-shrink: 0;
        }

        .carousel-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.2);
        }

        .carousel-img {
            height: 200px; /* Aumentado para melhor visualização */
            object-fit: cover;
            width: 100%;
            border-bottom: 3px solid var(--primary-color);
        }

        .carousel-content {
            padding: 20px;
            height: 220px;
            display: flex;
            flex-direction: column;
        }

        .carousel-title {
            font-size: 1.25rem;
            margin-bottom: 8px;
            color: var(--dark-color);
            font-weight: 700;
        }

        .carousel-price {
            font-size: 1.4rem;
            font-weight: 800;
            color: var(--secondary-color);
            margin-bottom: 12px;
        }

        .carousel-description {
            font-size: 1rem;
            margin-bottom: 20px;
            color: #555;
            line-height: 1.5;
            flex-grow: 1;
        }

        /* Benefícios */
        .benefits {
            background-color: var(--gray-color);
            position: relative;
            z-index: 1;
        }

        .benefits-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 25px;
            margin-top: 40px;
        }

        .benefit-item {
            text-align: center;
            padding: 30px 25px;
            background-color: var(--light-color);
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-top: 4px solid var(--primary-color);
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            flex: 1;
            min-width: 220px;
            max-width: 280px;
            margin: 0 5px;
        }

        .benefit-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        }

        .benefit-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            display: inline-block;
            background-color: rgba(255, 204, 0, 0.1);
            width: 80px;
            height: 80px;
            line-height: 80px;
            border-radius: 50%;
            margin: 0 auto 20px;
        }

        .benefit-title {
            font-size: 1.4rem;
            margin-bottom: 15px;
            color: var(--dark-color);
            font-weight: 700;
        }

        .benefit-text {
            font-size: 1rem;
            color: #555;
            line-height: 1.6;
            flex-grow: 1;
        }

        /* Para quem é */
        .for-who {
            background-color: var(--light-color);
            position: relative;
            overflow: hidden;
        }

        .for-who-container {
            background-color: var(--primary-color);
            padding: 50px 40px;
            border-radius: 15px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            position: relative;
            z-index: 1;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .for-who-container:before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 150px;
            height: 150px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(50%, -50%);
            z-index: -1;
        }

        .for-who-title {
            font-size: 2rem;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 700;
            color: var(--dark-color);
            position: relative;
            display: inline-block;
            padding-bottom: 10px;
            margin-left: auto;
            margin-right: auto;
            width: 100%;
        }

        .for-who-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background-color: var(--secondary-color);
        }

        .for-who-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .for-who-item {
            margin-bottom: 20px;
            padding-left: 40px;
            position: relative;
            font-size: 1.2rem;
            line-height: 1.5;
            font-weight: 500;
        }

        .for-who-item:last-child {
            margin-bottom: 0;
        }

        .for-who-item:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--secondary-color);
            font-weight: bold;
            font-size: 1.4rem;
            background-color: rgba(230, 0, 0, 0.1);
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
        }

        /* Sobre o Kina */
        .about {
            background-color: var(--gray-color);
            position: relative;
        }

        .about-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            margin-top: 40px;
        }

        .about-text h3 {
            font-size: 2rem;
            margin-bottom: 25px;
            color: var(--dark-color);
            position: relative;
            padding-bottom: 15px;
            font-weight: 700;
        }

        .about-text h3:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 3px;
            background-color: var(--primary-color);
        }

        .about-text p {
            margin-bottom: 20px;
            font-size: 1.05rem;
            line-height: 1.7;
            color: #444;
        }

        .about-image img {
            border-radius: 15px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            border: 5px solid white;
            width: 100%;
            height: auto;
            transition: transform 0.3s ease;
        }

        .about-image img:hover {
            transform: scale(1.02);
        }

        /* Unidades */
        .locations {
            background-color: var(--light-color);
            position: relative;
        }

        .locations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .location-item {
            background-color: var(--gray-color);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .location-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .location-img {
            height: 220px;
            object-fit: cover;
            width: 100%;
            border-bottom: 4px solid var(--primary-color);
        }

        .location-content {
            padding: 25px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .location-title {
            font-size: 1.5rem;
            margin-bottom: 12px;
            color: var(--dark-color);
            font-weight: 700;
            position: relative;
            padding-bottom: 10px;
        }

        .location-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--primary-color);
        }

        .location-address {
            font-size: 1rem;
            color: #555;
            margin-bottom: 20px;
            line-height: 1.6;
            flex-grow: 1;
        }

        .location-item .btn-cta {
            align-self: flex-start;
            margin-top: auto;
        }

        /* FAQ */
        .faq {
            background-color: var(--gray-color);
            position: relative;
        }

        .faq-item {
            margin-bottom: 20px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: box-shadow 0.3s ease;
        }

        .faq-item:hover {
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
        }

        .faq-question {
            background-color: var(--light-color);
            padding: 20px 25px;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.1rem;
            color: var(--dark-color);
            transition: background-color 0.3s ease;
            border-left: 4px solid var(--primary-color);
        }

        .faq-question:hover {
            background-color: #f9f9f9;
        }

        .faq-question:after {
            content: '+';
            font-size: 1.8rem;
            color: var(--primary-color);
            font-weight: 700;
            transition: transform 0.3s ease;
        }

        .faq-answer {
            padding: 0 25px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.4s ease;
            background-color: #fff;
            line-height: 1.7;
            color: #444;
            font-size: 1.05rem;
        }

        .faq-item.active .faq-question {
            background-color: #f9f9f9;
        }

        .faq-item.active .faq-question:after {
            content: '-';
            transform: rotate(180deg);
        }

        .faq-item.active .faq-answer {
            padding: 25px;
            max-height: 1000px;
        }

        /* Footer */
        .footer {
            background-color: var(--dark-color);
            color: var(--light-color);
            padding: 70px 0 30px;
            position: relative;
        }

        .footer:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 50px;
            margin-bottom: 40px;
        }

        .footer-info h3 {
            font-size: 1.5rem;
            margin-bottom: 25px;
            color: var(--primary-color);
            font-weight: 700;
            position: relative;
            padding-bottom: 12px;
        }

        .footer-info h3:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--primary-color);
        }

        .footer-info p {
            margin-bottom: 15px;
            font-size: 1rem;
            line-height: 1.7;
            color: rgba(255, 255, 255, 0.8);
        }

        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--light-color);
            border-radius: 50%;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 1.3rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .social-link:hover {
            background-color: var(--primary-color);
            color: var(--dark-color);
            transform: translateY(-5px);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 25px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.95rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* Botão flutuante para pedido */
        .floating-cta {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 999;
        }

        .floating-cta .btn-cta {
            padding: 15px 30px;
            font-size: 1.1rem;
            box-shadow: 0 5px 20px rgba(230, 0, 0, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.2);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 5px 20px rgba(230, 0, 0, 0.4);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 8px 25px rgba(230, 0, 0, 0.6);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 5px 20px rgba(230, 0, 0, 0.4);
            }
        }

        /* Responsividade */
        @media (max-width: 1024px) {
            .container {
                width: 95%;
            }

            .about-grid {
                gap: 40px;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .top-announcement-bar {
                font-size: 13px;
            }

            .benefit-item {
                min-width: 200px;
                max-width: 45%;
            }
        }

        @media (max-width: 768px) {
            .header {
                height: auto;
                padding: 25px 0; /* Ajustado para telas menores, mas ainda com espaço extra */
                top: 32px; /* Ajustado para telas médias */
            }

            .top-announcement-bar {
                font-size: 13px;
                padding: 7px 0;
            }

            .announcement-container {
                padding: 0 15px;
            }

            .hero {
                padding: 180px 0 50px; /* Ajustado para compensar a barra de anúncio */
            }

            .benefit-item {
                min-width: 45%;
                max-width: 45%;
            }

            .hero-title {
                font-size: 2.2rem;
                margin-bottom: 15px;
            }

            .hero-subtitle {
                font-size: 1.1rem;
                margin-bottom: 25px;
            }

            .section {
                padding: 50px 0;
            }

            .section-title {
                font-size: 1.9rem;
                margin-bottom: 25px;
            }

            .about-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .about-image {
                order: -1;
            }

            .about-text h3 {
                font-size: 1.8rem;
            }

            .for-who-container {
                padding: 35px 25px;
            }

            .for-who-title {
                font-size: 1.8rem;
            }

            .for-who-item {
                font-size: 1.1rem;
            }

            .benefit-item {
                padding: 25px 20px;
            }

            .benefit-title {
                font-size: 1.3rem;
            }

            .carousel {
                margin: 30px 0 20px;
            }

            .carousel-item {
                width: 260px;
            }

            .footer {
                padding: 50px 0 25px;
            }

            .footer-grid {
                gap: 35px;
            }
        }

        @media (max-width: 576px) {
            .container {
                width: 92%;
                padding: 0 15px;
            }

            .header-cta {
                padding: 8px 15px;
                font-size: 0.85rem;
            }

            .top-announcement-bar {
                font-size: 11px;
                padding: 6px 0;
                line-height: 1.3;
            }

            .announcement-container {
                padding: 0 10px;
            }

            .header {
                top: 28px; /* Ajustado para telas pequenas */
            }

            .benefit-item {
                min-width: 100%;
                max-width: 100%;
                margin: 0;
            }

            .hero {
                padding: 170px 0 40px; /* Ajustado para compensar a barra de anúncio */
            }

            .hero-title {
                font-size: 1.9rem;
                margin-bottom: 12px;
            }

            .hero-subtitle {
                font-size: 1rem;
                margin-bottom: 20px;
                line-height: 1.5;
            }

            .btn-cta {
                padding: 12px 25px;
                font-size: 0.9rem;
            }

            .section {
                padding: 40px 0;
            }

            .section-title {
                font-size: 1.7rem;
            }

            .carousel-item {
                width: 240px;
            }

            .carousel-img {
                height: 180px;
            }

            .carousel-title {
                font-size: 1.15rem;
            }

            .carousel-price {
                font-size: 1.3rem;
            }

            .carousel-description {
                font-size: 0.95rem;
                margin-bottom: 15px;
            }

            .benefit-icon {
                width: 70px;
                height: 70px;
                line-height: 70px;
                font-size: 2.5rem;
            }

            .benefit-title {
                font-size: 1.2rem;
            }

            .for-who-container {
                padding: 30px 20px;
            }

            .for-who-title {
                font-size: 1.6rem;
            }

            .for-who-item {
                font-size: 1rem;
                padding-left: 35px;
            }

            .for-who-item:before {
                width: 25px;
                height: 25px;
                line-height: 25px;
                font-size: 1.2rem;
            }

            .about-text h3 {
                font-size: 1.6rem;
            }

            .about-text p {
                font-size: 1rem;
            }

            .location-img {
                height: 180px;
            }

            .location-title {
                font-size: 1.3rem;
            }

            .faq-question {
                padding: 15px 20px;
                font-size: 1rem;
            }

            .faq-answer {
                font-size: 0.95rem;
            }

            .faq-item.active .faq-answer {
                padding: 20px;
            }

            .footer-info h3 {
                font-size: 1.3rem;
            }

            .social-link {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }

            .floating-cta {
                bottom: 20px;
                right: 20px;
            }

            .floating-cta .btn-cta {
                padding: 12px 25px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Barra de anúncio deslizante no topo -->
    <div class="top-announcement-bar">
        <div class="announcement-container">
            <div class="announcement-slider">
                <div class="announcement-slide">
                    <p>Frete grátis acima de R$ 50 | Peça agora pelo iFood</p>
                </div>
                <div class="announcement-slide">
                    <p>Cadastre-se e ganhe 5% OFF na primeira compra*, Cupom FIRST5</p>
                </div>
                <div class="announcement-slide">
                    <p>Combinados a partir de R$ 39,90 | Delivery rápido para Fortaleza</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Header Fixo -->
    <header class="header">
        <div class="container header-container">
            <div class="logo-container">
                <!-- Logo do Kina -->
                <!-- Logo do Kina usando o arquivo PNG original -->
                <img src="images/logo kina.png" alt="Kina Restaurante" style="width: 120px; height: auto; margin: 5px 0;">


            </div>
            <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703" target="_blank" class="btn-cta header-cta">Pedir no iFood</a>
        </div>
    </header>

    <!-- Sessão 01 - Hero com Headline e CTA -->
    <section class="hero" id="home">
        <div class="container">
            <h1 class="hero-title">Pediu Kina, chegou! Peça agora pelo iFood</h1>
            <p class="hero-subtitle">Receba comida japonesa e asiática com qualidade, rapidez e preço justo diretamente na sua casa</p>
            <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703" target="_blank" class="btn-cta">Fazer pedido agora</a>

            <!-- Carrossel de produtos populares -->
            <div class="carousel-wrapper">
                <div class="carousel-controls">
                    <button class="carousel-control prev" aria-label="Anterior">&lt;</button>
                    <button class="carousel-control next" aria-label="Próximo">&gt;</button>
                </div>
                <div class="carousel">
                    <div class="carousel-container">
                        <!-- Produto 1 -->
                        <div class="carousel-item">
                            <img src="images/temaki-especial.jpg" alt="Temaki Especial" class="carousel-img">
                            <div class="carousel-content">
                                <h3 class="carousel-title">Temaki Especial</h3>
                                <p class="carousel-price">R$ 29,90</p>
                                <p class="carousel-description">Temaki recheado com salmão, cream cheese e cebolinha</p>
                                <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=temaki-especial" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>
                            </div>
                        </div>

                        <!-- Produto 2 -->
                        <div class="carousel-item">
                            <img src="images/yakisoba-carne.jpg" alt="Yakisoba de Carne" class="carousel-img">
                            <div class="carousel-content">
                                <h3 class="carousel-title">Yakisoba de Carne</h3>
                                <p class="carousel-price">R$ 39,90</p>
                                <p class="carousel-description">Macarrão oriental com legumes frescos e carne</p>
                                <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=yakisoba-carne" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>
                            </div>
                        </div>

                        <!-- Produto 3 -->
                        <div class="carousel-item">
                            <img src="images/combinado-30.jpg" alt="Combinado 30 Peças" class="carousel-img">
                            <div class="carousel-content">
                                <h3 class="carousel-title">Combinado 30 Peças</h3>
                                <p class="carousel-price">R$ 89,90</p>
                                <p class="carousel-description">Seleção de sushis e sashimis para 2 pessoas</p>
                                <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=combinado-30-pecas" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>
                            </div>
                        </div>

                        <!-- Produto 4 -->
                        <div class="carousel-item">
                            <img src="images/hot-roll.jpg" alt="Hot Roll" class="carousel-img">
                            <div class="carousel-content">
                                <h3 class="carousel-title">Hot Roll (10 unid)</h3>
                                <p class="carousel-price">R$ 32,90</p>
                                <p class="carousel-description">Sushi empanado e frito com recheio especial</p>
                                <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=hot-roll" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>
                            </div>
                        </div>

                        <!-- Produto 5 -->
                        <div class="carousel-item">
                            <img src="images/sashimi-salmao.jpg" alt="Sashimi de Salmão" class="carousel-img">
                            <div class="carousel-content">
                                <h3 class="carousel-title">Sashimi de Salmão</h3>
                                <p class="carousel-price">R$ 45,90</p>
                                <p class="carousel-description">10 fatias de salmão fresco fatiado na hora</p>
                                <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=sashimi-salmao" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>
                            </div>
                        </div>

                        <!-- Produto 6 -->
                        <div class="carousel-item">
                            <img src="images/uramaki-philadelphia.jpg" alt="Uramaki Philadelphia" class="carousel-img">
                            <div class="carousel-content">
                                <h3 class="carousel-title">Uramaki Philadelphia</h3>
                                <p class="carousel-price">R$ 35,90</p>
                                <p class="carousel-description">8 unidades com salmão, cream cheese e cebolinha</p>
                                <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=uramaki-philadelphia" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>
                            </div>
                        </div>

                        <!-- Produto 7 -->
                        <div class="carousel-item">
                            <img src="images/gyoza.jpg" alt="Gyoza" class="carousel-img">
                            <div class="carousel-content">
                                <h3 class="carousel-title">Gyoza (5 unid)</h3>
                                <p class="carousel-price">R$ 28,90</p>
                                <p class="carousel-description">Pastéis japoneses grelhados recheados com carne</p>
                                <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=gyoza" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>
                            </div>
                        </div>

                        <!-- Produto 8 -->
                        <div class="carousel-item">
                            <img src="images/tempura-camarao.jpg" alt="Tempura de Camarão" class="carousel-img">
                            <div class="carousel-content">
                                <h3 class="carousel-title">Tempura de Camarão</h3>
                                <p class="carousel-price">R$ 49,90</p>
                                <p class="carousel-description">Camarões empanados em massa leve e crocante</p>
                                <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=tempura-camarao" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>
                            </div>
                        </div>

                        <!-- Produto 9 -->
                        <div class="carousel-item">
                            <img src="images/hossomaki-kani.jpg" alt="Hossomaki de Kani" class="carousel-img">
                            <div class="carousel-content">
                                <h3 class="carousel-title">Hossomaki de Kani</h3>
                                <p class="carousel-price">R$ 25,90</p>
                                <p class="carousel-description">8 unidades de sushi fino com kani e arroz</p>
                                <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=hossomaki-kani" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>
                            </div>
                        </div>

                        <!-- Produto 10 -->
                        <div class="carousel-item">
                            <img src="images/niguiri-salmao.jpg" alt="Niguiri de Salmão" class="carousel-img">
                            <div class="carousel-content">
                                <h3 class="carousel-title">Niguiri de Salmão</h3>
                                <p class="carousel-price">R$ 29,90</p>
                                <p class="carousel-description">6 unidades de bolinho de arroz com fatia de salmão</p>
                                <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=niguiri-salmao" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Sessão 02 - Benefícios do Delivery -->
    <section class="section benefits" id="benefits">
        <div class="container">
            <h2 class="section-title">Por que pedir no Kina?</h2>
            <div class="benefits-grid">
                <!-- Benefício 1 -->
                <div class="benefit-item">
                    <div class="benefit-icon">🔥</div>
                    <h3 class="benefit-title">Sabor Autêntico</h3>
                    <p class="benefit-text">Receitas tradicionais japonesas com toque cearense. Nosso sushi é preparado por chefs especializados que dominam a arte da culinária oriental.</p>
                </div>

                <!-- Benefício 2 -->
                <div class="benefit-item">
                    <div class="benefit-icon">⚡</div>
                    <h3 class="benefit-title">Delivery Express</h3>
                    <p class="benefit-text">Entrega ultrarrápida para sua comida chegar quentinha e no ponto. Nosso tempo médio de entrega é de apenas 35 minutos para Fortaleza.</p>
                </div>

                <!-- Benefício 3 -->
                <div class="benefit-item">
                    <div class="benefit-icon">🐟</div>
                    <h3 class="benefit-title">Peixe Fresco Diário</h3>
                    <p class="benefit-text">Salmão e peixes selecionados recebidos diariamente. Trabalhamos apenas com fornecedores certificados que garantem a máxima qualidade.</p>
                </div>

                <!-- Benefício 4 -->
                <div class="benefit-item">
                    <div class="benefit-icon">💰</div>
                    <h3 class="benefit-title">Melhor Custo-Benefício</h3>
                    <p class="benefit-text">Porções generosas a preços justos. Nossos combinados são pensados para satisfazer seu apetite sem pesar no bolso. Experimente e comprove!</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Sessão 03 - Para Quem é o Kina -->
    <section class="section for-who" id="for-who">
        <div class="container">
            <h2 class="section-title">Para Quem é o Kina?</h2>
            <div class="for-who-container">
                <h3 class="for-who-title">Ideal para quem...</h3>
                <ul class="for-who-list">
                    <li class="for-who-item">Ama comida japonesa, mas quer variedade asiática</li>
                    <li class="for-who-item">Quer comer bem sem gastar muito</li>
                    <li class="for-who-item">Gosta de pedir delivery com agilidade e sabor</li>
                    <li class="for-who-item">Busca opções para compartilhar com amigos e família</li>
                    <li class="for-who-item">Valoriza qualidade e frescor nos ingredientes</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- Sessão 04 - Sobre o Kina -->
    <section class="section about" id="about">
        <div class="container">
            <h2 class="section-title">Sobre o Kina Restaurante</h2>
            <div class="about-grid">
                <div class="about-text">
                    <h3>Nossa História</h3>
                    <p>O Kina Restaurante nasceu da paixão pela culinária asiática e do desejo de oferecer aos fortalezenses uma experiência gastronômica única, com sabores autênticos e preços acessíveis.</p>
                    <p>Desde nossa fundação, temos como missão levar o melhor da culinária japonesa e asiática para todos, com qualidade, frescor e atendimento excepcional.</p>
                    <p>Ao longo dos anos, conquistamos o carinho dos nossos clientes e expandimos para três unidades em Fortaleza, mantendo sempre o compromisso com a excelência em cada prato que servimos.</p>
                </div>
                <div class="about-image">
                    <!-- Substituir com imagem real do restaurante ou equipe -->
                    <img src="https://via.placeholder.com/600x400" alt="Kina Restaurante">
                </div>
            </div>
        </div>
    </section>

    <!-- Sessão 05 - Unidades -->
    <section class="section locations" id="locations">
        <div class="container">
            <h2 class="section-title">Nossas Unidades</h2>
            <div class="locations-grid">
                <!-- Unidade 1 -->
                <div class="location-item">
                    <img src="https://via.placeholder.com/400x300" alt="Kina Shopping Iguatemi" class="location-img">
                    <div class="location-content">
                        <h3 class="location-title">Kina Iguatemi</h3>
                        <p class="location-address">Av. Washington Soares, 85 - Edson Queiroz<br>Shopping Iguatemi - Praça de Alimentação</p>
                        <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703" target="_blank" class="btn-cta" style="padding: 10px 20px; font-size: 0.9rem;">Pedir desta unidade</a>
                    </div>
                </div>

                <!-- Unidade 2 -->
                <div class="location-item">
                    <img src="https://via.placeholder.com/400x300" alt="Kina Shopping Del Paseo" class="location-img">
                    <div class="location-content">
                        <h3 class="location-title">Kina Del Paseo</h3>
                        <p class="location-address">Av. Santos Dumont, 3131 - Aldeota<br>Shopping Del Paseo - Praça de Alimentação</p>
                        <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703" target="_blank" class="btn-cta" style="padding: 10px 20px; font-size: 0.9rem;">Pedir desta unidade</a>
                    </div>
                </div>

                <!-- Unidade 3 -->
                <div class="location-item">
                    <img src="https://via.placeholder.com/400x300" alt="Kina Benfica" class="location-img">
                    <div class="location-content">
                        <h3 class="location-title">Kina Benfica</h3>
                        <p class="location-address">Av. Carapinima, 2200 - Benfica<br>Próximo à Universidade Federal do Ceará</p>
                        <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703" target="_blank" class="btn-cta" style="padding: 10px 20px; font-size: 0.9rem;">Pedir desta unidade</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Sessão 06 - FAQ -->
    <section class="section faq" id="faq">
        <div class="container">
            <h2 class="section-title">Perguntas Frequentes</h2>

            <!-- Pergunta 1 -->
            <div class="faq-item">
                <div class="faq-question">Quais tipos de comida o Kina oferece?</div>
                <div class="faq-answer">
                    <p>O Kina Restaurante é especializado em culinária japonesa e asiática, oferecendo uma variedade de pratos como sushi, sashimi, temaki, yakisoba, combinados e pratos quentes. Também temos opções de entradas, bebidas e sobremesas típicas.</p>
                </div>
            </div>

            <!-- Pergunta 2 -->
            <div class="faq-item">
                <div class="faq-question">Posso agendar pedidos para uma data específica?</div>
                <div class="faq-answer">
                    <p>Sim! Você pode agendar seu pedido pelo iFood para o dia e horário que preferir. Basta selecionar a opção "Agendar" ao finalizar seu pedido no aplicativo.</p>
                </div>
            </div>

            <!-- Pergunta 3 -->
            <div class="faq-item">
                <div class="faq-question">O Kina entrega em qual região de Fortaleza?</div>
                <div class="faq-answer">
                    <p>Atendemos diversas regiões de Fortaleza, com entrega disponível para bairros como Aldeota, Meireles, Varjota, Cocó, Edson Queiroz, Benfica, Fátima, entre outros. A disponibilidade e taxa de entrega podem ser verificadas diretamente no iFood.</p>
                </div>
            </div>

            <!-- Pergunta 4 -->
            <div class="faq-item">
                <div class="faq-question">Como encontro o Kina no iFood?</div>
                <div class="faq-answer">
                    <p>Você pode encontrar o Kina no iFood buscando por "Kina Restaurante" ou "Kina Japonês" na barra de pesquisa. Também pode acessar diretamente através do link em nosso site ou clicando nos botões de pedido desta página.</p>
                </div>
            </div>

            <!-- Pergunta 5 -->
            <div class="faq-item">
                <div class="faq-question">O Kina tem opções vegetarianas?</div>
                <div class="faq-answer">
                    <p>Sim! Oferecemos diversas opções vegetarianas, como temakis, hossomakis e uramakis de legumes, além de yakisoba vegetariano e outras opções. Todas estão identificadas em nosso cardápio no iFood.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-info">
                    <h3>Kina Restaurante</h3>
                    <p>Comida japonesa e asiática de qualidade para toda Fortaleza.</p>
                    <div class="social-links">
                        <a href="https://www.instagram.com/kinarestaurante/" target="_blank" class="social-link">IG</a>
                        <a href="#" class="social-link">FB</a>
                        <a href="#" class="social-link">YT</a>
                        <a href="#" class="social-link">WA</a>
                    </div>
                </div>

                <div class="footer-info">
                    <h3>Horário de Funcionamento</h3>
                    <p>Segunda a Sábado: 11h às 22h</p>
                    <p>Domingo: 11h às 21h</p>
                    <p>Delivery: Todos os dias das 11h às 22h</p>
                </div>

                <div class="footer-info">
                    <h3>Contato</h3>
                    <p>📞 (85) 3000-0000</p>
                    <p>✉️ <EMAIL></p>
                    <p>📱 WhatsApp: (85) 99999-9999</p>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2023 Kina Restaurante. Todos os direitos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- Botão flutuante para pedido -->
    <div class="floating-cta">
        <a href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703" target="_blank" class="btn-cta">Pedir no iFood</a>
    </div>

    <script>
        // Script para o FAQ
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', () => {
                const item = question.parentNode;
                item.classList.toggle('active');
            });
        });

        // Script para rolagem suave
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();

                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Script para o carrossel dinâmico
        document.addEventListener('DOMContentLoaded', function() {
            const carousel = document.querySelector('.carousel');
            const carouselContainer = document.querySelector('.carousel-container');
            const prevButton = document.querySelector('.carousel-control.prev');
            const nextButton = document.querySelector('.carousel-control.next');
            const itemWidth = 305; // Largura do item + gap
            let scrollAmount = 0;
            let autoScrollInterval;

            // Função para rolar para a esquerda
            function scrollLeft() {
                scrollAmount -= itemWidth;
                if (scrollAmount < 0) {
                    // Voltar para o final quando chegar ao início
                    scrollAmount = carouselContainer.scrollWidth - carousel.clientWidth;
                }
                carousel.scrollTo({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }

            // Função para rolar para a direita
            function scrollRight() {
                scrollAmount += itemWidth;
                if (scrollAmount > carouselContainer.scrollWidth - carousel.clientWidth) {
                    // Voltar para o início quando chegar ao final
                    scrollAmount = 0;
                }
                carousel.scrollTo({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }

            // Adicionar eventos de clique aos botões
            prevButton.addEventListener('click', scrollLeft);
            nextButton.addEventListener('click', scrollRight);

            // Auto-scroll a cada 5 segundos
            function startAutoScroll() {
                autoScrollInterval = setInterval(scrollRight, 5000);
            }

            // Parar auto-scroll quando o mouse estiver sobre o carrossel
            carousel.addEventListener('mouseenter', () => {
                clearInterval(autoScrollInterval);
            });

            // Reiniciar auto-scroll quando o mouse sair do carrossel
            carousel.addEventListener('mouseleave', startAutoScroll);

            // Iniciar auto-scroll
            startAutoScroll();

            // Ajustar para dispositivos móveis
            function adjustCarouselForMobile() {
                if (window.innerWidth < 768) {
                    // Em dispositivos móveis, mostrar apenas um item por vez
                    const carouselItems = document.querySelectorAll('.carousel-item');
                    carouselItems.forEach(item => {
                        item.style.width = (carousel.clientWidth - 50) + 'px';
                    });
                }
            }

            // Chamar a função de ajuste ao carregar e redimensionar
            adjustCarouselForMobile();
            window.addEventListener('resize', adjustCarouselForMobile);
        });
    </script>
</body>
</html>
