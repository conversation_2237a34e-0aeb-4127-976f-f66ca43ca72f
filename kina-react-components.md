# Componentes React para a Landing Page do Kina

Este documento descreve os componentes React que serão implementados para melhorar a landing page do Kina Restaurante.

## 1. Compo<PERSON><PERSON>

```jsx
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import './Header.css';

const Header = () => {
  const [scrolled, setScrolled] = useState(false);
  
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  
  return (
    <header className={`header ${scrolled ? 'scrolled' : ''}`}>
      <div className="container header-container">
        <motion.div 
          className="logo-container"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <img src="/images/logo-kina.png" alt="Kina Restaurante" className="logo" />
        </motion.div>
        
        <motion.a 
          href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703" 
          target="_blank" 
          rel="noopener noreferrer"
          className="btn-cta header-cta"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Pedir no iFood
        </motion.a>
      </div>
    </header>
  );
};

export default Header;
```

## 2. Componente AnnouncementBar

```jsx
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './AnnouncementBar.css';

const AnnouncementBar = () => {
  const announcements = [
    "Frete grátis acima de R$ 50 | Peça agora pelo iFood",
    "Cadastre-se e ganhe 5% OFF na primeira compra*, Cupom FIRST5",
    "Combinados a partir de R$ 39,90 | Delivery rápido para Fortaleza"
  ];
  
  const [currentIndex, setCurrentIndex] = useState(0);
  
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % announcements.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [announcements.length]);
  
  return (
    <div className="top-announcement-bar">
      <div className="announcement-container">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="announcement-content"
          >
            <p>{announcements[currentIndex]}</p>
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AnnouncementBar;
```

## 3. Componente ProductCarousel

```jsx
import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './ProductCarousel.css';

const ProductCard = ({ product, index }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  return (
    <motion.div 
      className="carousel-item"
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -10, transition: { duration: 0.2 } }}
    >
      <img 
        src={product.image} 
        alt={product.title} 
        className="carousel-img"
        loading="lazy"
      />
      <div className="carousel-content">
        <h3 className="carousel-title">{product.title}</h3>
        <p className="carousel-price">R$ {product.price.toFixed(2)}</p>
        <p className="carousel-description">{product.description}</p>
        <motion.a 
          href={product.url} 
          target="_blank" 
          rel="noopener noreferrer"
          className="btn-cta btn-cta-secondary"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Pedir
        </motion.a>
      </div>
    </motion.div>
  );
};

const ProductCarousel = ({ products }) => {
  const [width, setWidth] = useState(0);
  const carousel = useRef();
  
  useEffect(() => {
    setWidth(carousel.current.scrollWidth - carousel.current.offsetWidth);
  }, []);
  
  return (
    <div className="carousel-wrapper">
      <motion.div ref={carousel} className="carousel">
        <motion.div 
          className="carousel-container"
          drag="x"
          dragConstraints={{ right: 0, left: -width }}
          whileTap={{ cursor: 'grabbing' }}
        >
          {products.map((product, index) => (
            <ProductCard key={product.id} product={product} index={index} />
          ))}
        </motion.div>
      </motion.div>
      
      <div className="carousel-dots">
        {products.map((_, index) => (
          <motion.button
            key={index}
            className="carousel-dot"
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => {
              const scrollAmount = index * 305;
              carousel.current.scrollTo({
                left: scrollAmount,
                behavior: 'smooth'
              });
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default ProductCarousel;
```

## 4. Componente LocationMap

```jsx
import React, { useState } from 'react';
import { GoogleMap, LoadScript, Marker, InfoWindow } from '@react-google-maps/api';
import { motion } from 'framer-motion';
import './LocationMap.css';

const LocationMap = ({ locations }) => {
  const [selectedLocation, setSelectedLocation] = useState(null);
  
  const mapStyles = {
    height: "400px",
    width: "100%",
    borderRadius: "12px",
    boxShadow: "0 8px 20px rgba(0, 0, 0, 0.15)"
  };
  
  const defaultCenter = {
    lat: -3.7319, // Fortaleza latitude
    lng: -38.5267 // Fortaleza longitude
  };
  
  return (
    <div className="location-map-container">
      <LoadScript googleMapsApiKey="YOUR_GOOGLE_MAPS_API_KEY">
        <GoogleMap
          mapContainerStyle={mapStyles}
          zoom={13}
          center={defaultCenter}
        >
          {locations.map(location => (
            <Marker
              key={location.id}
              position={location.position}
              onClick={() => setSelectedLocation(location)}
              icon={{
                url: '/images/marker-icon.png',
                scaledSize: { width: 40, height: 40 }
              }}
            />
          ))}
          
          {selectedLocation && (
            <InfoWindow
              position={selectedLocation.position}
              onCloseClick={() => setSelectedLocation(null)}
            >
              <div className="info-window">
                <h3>{selectedLocation.title}</h3>
                <p>{selectedLocation.address}</p>
                <motion.a 
                  href={selectedLocation.ifoodUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="btn-cta btn-cta-small"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Pedir desta unidade
                </motion.a>
              </div>
            </InfoWindow>
          )}
        </GoogleMap>
      </LoadScript>
    </div>
  );
};

export default LocationMap;
```

## 5. Componente ContactForm

```jsx
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import './ContactForm.css';

const ContactForm = () => {
  const { register, handleSubmit, formState: { errors }, reset } = useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  
  const onSubmit = async (data) => {
    setIsSubmitting(true);
    
    try {
      // Simulação de envio para API
      await new Promise(resolve => setTimeout(resolve, 1500));
      console.log('Form data:', data);
      setSubmitSuccess(true);
      reset();
      
      // Reset success message after 5 seconds
      setTimeout(() => {
        setSubmitSuccess(false);
      }, 5000);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="contact-form-container">
      <motion.form 
        className="contact-form"
        onSubmit={handleSubmit(onSubmit)}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="form-group">
          <label htmlFor="name">Nome</label>
          <input 
            type="text" 
            id="name" 
            {...register('name', { required: 'Nome é obrigatório' })}
            className={errors.name ? 'error' : ''}
          />
          {errors.name && <span className="error-message">{errors.name.message}</span>}
        </div>
        
        <div className="form-group">
          <label htmlFor="email">Email</label>
          <input 
            type="email" 
            id="email" 
            {...register('email', { 
              required: 'Email é obrigatório',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Email inválido'
              }
            })}
            className={errors.email ? 'error' : ''}
          />
          {errors.email && <span className="error-message">{errors.email.message}</span>}
        </div>
        
        <div className="form-group">
          <label htmlFor="message">Mensagem</label>
          <textarea 
            id="message" 
            rows="5"
            {...register('message', { required: 'Mensagem é obrigatória' })}
            className={errors.message ? 'error' : ''}
          ></textarea>
          {errors.message && <span className="error-message">{errors.message.message}</span>}
        </div>
        
        <motion.button 
          type="submit"
          className="btn-cta"
          disabled={isSubmitting}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {isSubmitting ? 'Enviando...' : 'Enviar Mensagem'}
        </motion.button>
        
        {submitSuccess && (
          <motion.div 
            className="success-message"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            Mensagem enviada com sucesso! Entraremos em contato em breve.
          </motion.div>
        )}
      </motion.form>
    </div>
  );
};

export default ContactForm;
```

## 6. Componente MiniCart

```jsx
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaShoppingCart, FaPlus, FaMinus, FaTrash } from 'react-icons/fa';
import './MiniCart.css';

const MiniCart = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [items, setItems] = useState([]);
  const [total, setTotal] = useState(0);
  
  useEffect(() => {
    // Calcular o total sempre que os itens mudarem
    const newTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    setTotal(newTotal);
  }, [items]);
  
  const addItem = (product) => {
    setItems(prevItems => {
      const existingItem = prevItems.find(item => item.id === product.id);
      
      if (existingItem) {
        return prevItems.map(item => 
          item.id === product.id 
            ? { ...item, quantity: item.quantity + 1 } 
            : item
        );
      } else {
        return [...prevItems, { ...product, quantity: 1 }];
      }
    });
  };
  
  const removeItem = (id) => {
    setItems(prevItems => prevItems.filter(item => item.id !== id));
  };
  
  const updateQuantity = (id, newQuantity) => {
    if (newQuantity < 1) return;
    
    setItems(prevItems => 
      prevItems.map(item => 
        item.id === id ? { ...item, quantity: newQuantity } : item
      )
    );
  };
  
  const toggleCart = () => {
    setIsOpen(!isOpen);
  };
  
  const checkout = () => {
    // Implementar lógica de checkout
    alert(`Pedido finalizado! Total: R$ ${total.toFixed(2)}`);
    setItems([]);
    setIsOpen(false);
  };
  
  return (
    <div className="mini-cart-container">
      <motion.button 
        className="cart-toggle"
        onClick={toggleCart}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <FaShoppingCart />
        {items.length > 0 && (
          <span className="cart-count">{items.length}</span>
        )}
      </motion.button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div 
            className="cart-panel"
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            <div className="cart-header">
              <h3>Seu Pedido</h3>
              <button className="close-btn" onClick={toggleCart}>×</button>
            </div>
            
            <div className="cart-items">
              {items.length === 0 ? (
                <p className="empty-cart">Seu carrinho está vazio</p>
              ) : (
                items.map(item => (
                  <motion.div 
                    key={item.id} 
                    className="cart-item"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    layout
                  >
                    <img src={item.image} alt={item.title} className="item-image" />
                    <div className="item-details">
                      <h4>{item.title}</h4>
                      <p className="item-price">R$ {item.price.toFixed(2)}</p>
                    </div>
                    <div className="item-quantity">
                      <button onClick={() => updateQuantity(item.id, item.quantity - 1)}>
                        <FaMinus />
                      </button>
                      <span>{item.quantity}</span>
                      <button onClick={() => updateQuantity(item.id, item.quantity + 1)}>
                        <FaPlus />
                      </button>
                    </div>
                    <button 
                      className="remove-item" 
                      onClick={() => removeItem(item.id)}
                    >
                      <FaTrash />
                    </button>
                  </motion.div>
                ))
              )}
            </div>
            
            {items.length > 0 && (
              <div className="cart-footer">
                <div className="cart-total">
                  <span>Total:</span>
                  <span>R$ {total.toFixed(2)}</span>
                </div>
                <motion.button 
                  className="btn-cta checkout-btn"
                  onClick={checkout}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Finalizar Pedido
                </motion.button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MiniCart;
```
