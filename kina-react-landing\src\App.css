/* Variáveis de cores baseadas na identidade visual do Kina */
:root {
  --primary-color: #ffcc00; /* <PERSON><PERSON> */
  --secondary-color: #e60000; /* Vermelho */
  --dark-color: #000000; /* Preto */
  --light-color: #ffffff; /* Branco */
  --gray-color: #f5f5f5; /* Cinza claro para fundos */
  --text-color: #333333;
  --font-primary: 'Montserrat', sans-serif;
  --font-secondary: '<PERSON>o', sans-serif;
}

/* Reset e estilos gerais */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  color: var(--text-color);
  line-height: 1.6;
  background-color: var(--light-color);
  overflow-x: hidden; /* Previne scroll horizontal */
}

.App {
  position: relative;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

p {
  margin-bottom: 1rem; /* Espaçamento consistente entre parágrafos */
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px; /* Aumentado para melhor espaçamento lateral */
}

.section {
  padding: 70px 0; /* Aumentado para melhor separação entre seções */
  margin-bottom: 0;
  position: relative;
}

.section-title {
  text-align: center;
  margin-bottom: 30px;
  font-size: 2rem;
  color: var(--dark-color);
  position: relative;
}

.section-title:after {
  content: '';
  display: block;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  margin: 10px auto;
}

/* Botão CTA principal */
.btn-cta {
  display: inline-block;
  padding: 15px 30px;
  background-color: var(--secondary-color);
  color: var(--light-color);
  text-decoration: none;
  border-radius: 50px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  box-shadow: 0 4px 10px rgba(230, 0, 0, 0.3);
}

.btn-cta:hover {
  background-color: #cc0000;
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(230, 0, 0, 0.4);
}

.btn-cta-secondary {
  background-color: var(--primary-color);
  color: var(--dark-color);
  box-shadow: 0 4px 10px rgba(255, 204, 0, 0.3);
}

.btn-cta-secondary:hover {
  background-color: #e6b800;
  box-shadow: 0 6px 15px rgba(255, 204, 0, 0.4);
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--dark-color);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  color: var(--light-color);
}

.loader {
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 5px solid var(--primary-color);
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header fixo */
.header {
  background-color: var(--light-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 36px; /* Ajustado para ficar abaixo da barra de anúncio */
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 15px 0;
}

/* Estilos para a barra de anúncio */
.top-announcement-bar {
  background-color: var(--primary-color);
  color: var(--dark-color);
  width: 100%;
  text-align: center;
  padding: 8px 0;
  font-size: 14px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.announcement-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
}

.header-cta {
  padding: 10px 25px;
  font-size: 0.95rem;
}

/* Hero Section */
.hero {
  background-color: var(--dark-color);
  color: var(--light-color);
  padding: 120px 0 100px; /* Ajustado para dar mais espaço */
  text-align: center;
  background-image:
    linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.9) 0%,
      rgba(20, 20, 20, 0.85) 40%,
      rgba(40, 40, 40, 0.8) 100%
    ),
    url('https://via.placeholder.com/1920x1080');
  background-size: cover;
  background-position: center;
  position: relative;
  overflow: hidden;
}

/* Elementos decorativos para o hero */
.hero::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(circle, var(--primary-color) 0%, rgba(255, 204, 0, 0) 70%);
  opacity: 0.3;
  z-index: 1;
}

.hero::after {
  content: '';
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: radial-gradient(circle, var(--secondary-color) 0%, rgba(230, 0, 0, 0) 70%);
  opacity: 0.2;
  z-index: 1;
}

.hero-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 5; /* Garantir que o conteúdo fique acima dos elementos decorativos */
}

.hero-title {
  font-size: 2.6rem; /* Ligeiramente aumentado */
  margin-bottom: 25px; /* Mais espaço entre título e subtítulo */
  font-weight: 800;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  max-width: 900px;
  letter-spacing: 0.5px;
}

.hero-subtitle {
  font-size: 1.25rem; /* Ligeiramente aumentado */
  margin-bottom: 35px; /* Mais espaço entre subtítulo e botão */
  font-weight: 300;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  padding: 0 10px; /* Adicionado padding lateral para melhor leitura */
}

.hero-cta-container {
  margin-top: 20px;
}

.hero-cta {
  padding: 16px 35px; /* Aumentado para maior destaque */
  font-size: 1.1rem; /* Aumentado para maior destaque */
  letter-spacing: 1px;
  border-radius: 50px;
  box-shadow: 0 8px 25px rgba(230, 0, 0, 0.4);
  position: relative;
  z-index: 2;
  width: auto; /* Ajustado para o tamanho do conteúdo */
  min-width: 220px; /* Garante uma largura mínima */
  display: inline-block;
  font-weight: 600; /* Mais destaque */
  margin-top: 10px; /* Espaço adicional acima do botão */
}

/* Decoração inferior do hero para transição suave */
.hero-bottom-decoration {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 60px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 100'%3E%3Cpath fill='%23ffffff' fill-opacity='1' d='M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,100L1360,100C1280,100,1120,100,960,100C800,100,640,100,480,100C320,100,160,100,80,100L0,100Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: cover;
  background-position: center;
  z-index: 3;
}

/* Carrossel de produtos */
.hero-carousel {
  background-color: var(--light-color);
  padding: 80px 0 60px; /* Reduzido para melhor equilíbrio */
  position: relative;
  margin-top: -40px; /* Ajustado para uma sobreposição mais sutil */
  z-index: 4;
}

.carousel-title {
  margin-bottom: 50px;
}

/* Ajuste para o título da seção de carrossel */
.hero-carousel .section-title:after {
  background-color: var(--primary-color);
}

.carousel-wrapper {
  position: relative;
  margin: 30px 0;
  width: 100%;
  overflow: hidden;
}

.carousel {
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: none;
  padding-bottom: 15px;
  position: relative;
  scroll-behavior: smooth;
}

.carousel::-webkit-scrollbar {
  display: none;
}

.carousel-container {
  display: inline-flex;
  gap: 25px;
  padding: 15px 10px;
}

.carousel-item {
  width: 280px;
  background-color: var(--light-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  display: inline-block;
  white-space: normal;
  border: 1px solid rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.carousel-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.2);
}

.carousel-content {
  padding: 20px;
  height: 220px;
  display: flex;
  flex-direction: column;
}

/* Benefícios */
.benefits {
  background-color: var(--gray-color);
  position: relative;
  z-index: 1;
}

.benefits-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 25px;
  margin-top: 40px;
}

.benefit-item {
  text-align: center;
  padding: 30px 25px;
  background-color: var(--light-color);
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-top: 4px solid var(--primary-color);
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex: 1;
  min-width: 220px;
  max-width: 280px;
  margin: 0 5px;
}

.benefit-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.benefit-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 20px;
  display: inline-block;
  background-color: rgba(255, 204, 0, 0.1);
  width: 80px;
  height: 80px;
  line-height: 80px;
  border-radius: 50%;
  margin: 0 auto 20px;
}

.benefit-title {
  font-size: 1.4rem;
  margin-bottom: 15px;
  color: var(--dark-color);
  font-weight: 700;
}

.benefit-text {
  font-size: 1rem;
  color: #555;
  line-height: 1.6;
  flex-grow: 1;
}

/* Footer */
.footer {
  background-color: var(--dark-color);
  color: var(--light-color);
  padding: 70px 0 30px;
  position: relative;
}

.footer:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 50px;
  margin-bottom: 40px;
}

.footer-info h3 {
  font-size: 1.5rem;
  margin-bottom: 25px;
  color: var(--primary-color);
  font-weight: 700;
  position: relative;
  padding-bottom: 12px;
}

.footer-info h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.footer-info p {
  margin-bottom: 15px;
  font-size: 1rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.8);
}

.footer-bottom {
  text-align: center;
  padding-top: 25px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Botão flutuante para pedido */
.floating-cta {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 100;
}

/* Responsividade */
/* Mobile-first approach - Estilos base são para mobile */

/* Tablets (768px e acima) */
@media (min-width: 768px) {
  .hero {
    padding: 130px 0 90px; /* Ajustado para melhor equilíbrio */
  }

  .hero-title {
    font-size: 3rem; /* Aumentado para telas maiores */
    margin-bottom: 25px;
  }

  .hero-subtitle {
    font-size: 1.35rem; /* Aumentado para telas maiores */
    margin-bottom: 35px;
    max-width: 85%; /* Limita a largura para melhor leitura */
  }

  .hero-cta {
    padding: 16px 40px;
    font-size: 1.1rem;
    width: auto;
  }

  .section {
    padding: 60px 0;
  }

  .header {
    padding: 15px 0; /* Mantido compacto mesmo em telas maiores */
    top: 36px;
    min-height: 70px;
  }

  .top-announcement-bar {
    padding: 8px 0;
    font-size: 14px;
  }

  .carousel-item {
    width: 280px;
  }

  /* Mostrar elementos decorativos em tablets */
  .hero-decoration-1,
  .hero-decoration-2 {
    display: block !important;
  }
}

/* Desktops (992px e acima) */
@media (min-width: 992px) {
  .hero {
    padding: 160px 0 90px;
  }

  .hero-title {
    font-size: 3.2rem;
    margin-bottom: 30px;
  }

  .hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 40px;
  }

  .hero-cta {
    padding: 16px 40px;
    font-size: 1.1rem;
  }

  .header {
    padding: 25px 0;
    min-height: 90px;
  }

  .section {
    padding: 80px 0;
  }
}

/* Telas grandes (1200px e acima) */
@media (min-width: 1200px) {
  .hero {
    padding: 180px 0 100px;
  }

  .hero-title {
    font-size: 3.5rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 50px;
  }

  .header {
    padding: 30px 0;
    min-height: 100px;
  }
}
