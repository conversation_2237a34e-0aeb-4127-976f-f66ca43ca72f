import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './App.css';

// Importação dos componentes
import Header from './components/Header/Header';
import AnnouncementBar from './components/AnnouncementBar/AnnouncementBar';
import ProductCarousel from './components/ProductCarousel/ProductCarousel';
import Benefits from './components/Benefits/Benefits';
import ForWho from './components/ForWho/ForWho';
import About from './components/About/About';
import Locations from './components/Locations/Locations';
import FAQ from './components/FAQ/FAQ';
import Footer from './components/Footer/Footer';
import FloatingCTA from './components/FloatingCTA/FloatingCTA';

// Componente Hero
const Hero = () => {
  // Efeito de paralaxe no scroll
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <section className="hero">
      {/* Elementos decorativos flutuantes */}
      {/* Elementos decorativos flutuantes - tamanhos reduzidos para mobile */}
      <motion.div
        className="hero-decoration hero-decoration-1"
        animate={{
          y: [0, 15, 0],
          rotate: [0, 5, 0]
        }}
        transition={{
          repeat: Infinity,
          duration: 6,
          ease: "easeInOut"
        }}
        style={{
          position: 'absolute',
          top: '15%',
          right: '5%',
          width: '50px', /* Reduzido para mobile */
          height: '50px', /* Reduzido para mobile */
          borderRadius: '50%',
          background: 'rgba(255, 204, 0, 0.15)',
          zIndex: 1,
          display: 'none', /* Escondido em mobile, será mostrado em telas maiores via CSS */
        }}
      />

      <motion.div
        className="hero-decoration hero-decoration-2"
        animate={{
          y: [0, -20, 0],
          rotate: [0, -8, 0]
        }}
        transition={{
          repeat: Infinity,
          duration: 8,
          ease: "easeInOut",
          delay: 1
        }}
        style={{
          position: 'absolute',
          bottom: '20%',
          left: '5%',
          width: '70px', /* Reduzido para mobile */
          height: '70px', /* Reduzido para mobile */
          borderRadius: '50%',
          background: 'rgba(230, 0, 0, 0.1)',
          zIndex: 1,
          display: 'none', /* Escondido em mobile, será mostrado em telas maiores via CSS */
        }}
      />

      {/* Elementos decorativos adicionais - tamanhos reduzidos para mobile */}
      <motion.div
        className="hero-decoration hero-decoration-3"
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.5, 0.7, 0.5]
        }}
        transition={{
          repeat: Infinity,
          duration: 4,
          ease: "easeInOut"
        }}
        style={{
          position: 'absolute',
          top: '40%',
          left: '5%',
          width: '30px', /* Reduzido para mobile */
          height: '30px', /* Reduzido para mobile */
          borderRadius: '50%',
          background: 'rgba(255, 255, 255, 0.1)',
          zIndex: 1,
          display: 'none', /* Escondido em mobile, será mostrado em telas maiores via CSS */
        }}
      />

      <motion.div
        className="hero-decoration hero-decoration-4"
        animate={{
          x: [0, 15, 0],
          y: [0, 10, 0]
        }}
        transition={{
          repeat: Infinity,
          duration: 5,
          ease: "easeInOut",
          delay: 0.5
        }}
        style={{
          position: 'absolute',
          bottom: '40%',
          right: '10%',
          width: '40px', /* Reduzido para mobile */
          height: '40px', /* Reduzido para mobile */
          borderRadius: '50%',
          background: 'rgba(255, 255, 255, 0.05)',
          zIndex: 1,
          display: 'none', /* Escondido em mobile, será mostrado em telas maiores via CSS */
        }}
      />

      {/* Elemento de destaque para o texto */}
      <motion.div
        className="hero-highlight"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1.5, delay: 0.2 }}
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '80%',
          height: '60%',
          background: 'radial-gradient(circle, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0) 70%)',
          zIndex: 1
        }}
      />

      <div className="hero-content" style={{
        transform: `translateY(${scrollY * 0.2}px)`,
        position: 'relative',
        zIndex: 2
      }}>
        <motion.h1
          className="hero-title"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.2 }}
        >
          Pediu Kina, chegou! Peça agora pelo iFood
        </motion.h1>
        <motion.p
          className="hero-subtitle"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.4 }}
        >
          Receba comida japonesa e asiática com qualidade, rapidez e preço justo diretamente na sua casa
        </motion.p>
        <motion.div
          className="hero-cta-container"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.6 }}
        >
          <motion.a
            href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-cta hero-cta"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            FAZER PEDIDO AGORA
          </motion.a>
        </motion.div>
      </div>

      {/* Decoração inferior para transição suave */}
      <div className="hero-bottom-decoration"></div>
    </section>
  );
};

function App() {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulação de carregamento
    const timer = setTimeout(() => {
      setLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <AnimatePresence>
      {loading ? (
        <motion.div
          className="loading-screen"
          initial={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="loader"></div>
          <h2>Carregando Kina Restaurante...</h2>
        </motion.div>
      ) : (
        <motion.div
          className="App"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <AnnouncementBar />
          <Header />
          <Hero />
          <div className="hero-carousel">
            <div className="container">
              <motion.h2
                className="section-title carousel-title"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                Nossos Pratos Mais Pedidos
              </motion.h2>
              <ProductCarousel />
            </div>
          </div>
          <Benefits />
          <ForWho />
          <About />
          <Locations />
          <FAQ />
          <Footer />
          <FloatingCTA />
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export default App;
