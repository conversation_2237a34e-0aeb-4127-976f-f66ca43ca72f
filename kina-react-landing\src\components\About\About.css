.about {
  background-color: var(--gray-color);
  position: relative;
  padding: 80px 0;
}

.about-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
  margin-bottom: 60px;
  align-items: center;
}

.about-text h3 {
  font-size: 1.8rem;
  margin-bottom: 25px;
  color: var(--dark-color);
  position: relative;
  padding-bottom: 15px;
}

.about-text h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
}

.about-text p {
  margin-bottom: 20px;
  font-size: 1.1rem;
  line-height: 1.7;
  color: #444;
}

.about-image {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  position: relative;
}

.about-image:before {
  content: '';
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  border: 3px solid var(--primary-color);
  border-radius: 10px;
  z-index: -1;
}

.about-image img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 15px;
  transition: transform 0.5s ease;
}

.about-image:hover img {
  transform: scale(1.03);
}

.about-values h3 {
  font-size: 1.8rem;
  margin-bottom: 40px;
  color: var(--dark-color);
  text-align: center;
  position: relative;
  padding-bottom: 15px;
}

.about-values h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 30px;
}

.value-item {
  background-color: var(--light-color);
  padding: 30px 25px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.value-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.value-icon {
  font-size: 2.5rem;
  margin-bottom: 20px;
  display: inline-block;
  background-color: rgba(255, 204, 0, 0.1);
  width: 70px;
  height: 70px;
  line-height: 70px;
  border-radius: 50%;
}

.value-item h4 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: var(--dark-color);
}

.value-item p {
  font-size: 1rem;
  color: #555;
  line-height: 1.6;
}

@media (max-width: 992px) {
  .about-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .about-image {
    order: -1;
  }
}

@media (max-width: 768px) {
  .about {
    padding: 60px 0;
  }
  
  .about-text h3,
  .about-values h3 {
    font-size: 1.6rem;
    margin-bottom: 20px;
  }
  
  .about-text p {
    font-size: 1rem;
  }
  
  .values-grid {
    grid-template-columns: 1fr;
  }
  
  .value-item {
    padding: 25px 20px;
  }
}
