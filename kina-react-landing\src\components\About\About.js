import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './About.css';

const About = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  return (
    <section className="section about" id="about" ref={ref}>
      <div className="container">
        <motion.h2 
          className="section-title"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.5 }}
        >
          Sobre o Kina Restaurante
        </motion.h2>
        
        <div className="about-grid">
          <motion.div 
            className="about-text"
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h3>Nossa História</h3>
            <p>O Kina Restaurante nasceu da paixão pela culinária asiática e do desejo de oferecer aos fortalezenses uma experiência gastronômica única, com sabores autênticos e preços acessíveis.</p>
            <p>Desde nossa fundação, temos como missão levar o melhor da culinária japonesa e asiática para todos, com qualidade, frescor e atendimento excepcional.</p>
            <p>Ao longo dos anos, conquistamos o carinho dos nossos clientes e expandimos para três unidades em Fortaleza, mantendo sempre o compromisso com a excelência em cada prato que servimos.</p>
          </motion.div>
          
          <motion.div 
            className="about-image"
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <img src="https://via.placeholder.com/500x400" alt="Kina Restaurante" />
          </motion.div>
        </div>
        
        <motion.div 
          className="about-values"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <h3>Nossos Valores</h3>
          <div className="values-grid">
            <div className="value-item">
              <div className="value-icon">🌟</div>
              <h4>Qualidade</h4>
              <p>Ingredientes frescos e selecionados para garantir o melhor sabor em cada prato.</p>
            </div>
            
            <div className="value-item">
              <div className="value-icon">💰</div>
              <h4>Acessibilidade</h4>
              <p>Preços justos para que todos possam desfrutar da autêntica culinária asiática.</p>
            </div>
            
            <div className="value-item">
              <div className="value-icon">🤝</div>
              <h4>Atendimento</h4>
              <p>Equipe treinada para oferecer uma experiência excepcional a cada cliente.</p>
            </div>
            
            <div className="value-item">
              <div className="value-icon">🌱</div>
              <h4>Sustentabilidade</h4>
              <p>Compromisso com práticas sustentáveis e responsabilidade ambiental.</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
