.top-announcement-bar {
  background-color: var(--primary-color);
  color: var(--dark-color);
  width: 100%;
  text-align: center;
  padding: 6px 0; /* Reduzido para mobile-first */
  font-size: 12px; /* Reduzido para mobile-first */
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.announcement-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  height: 20px; /* Altura fixa para evitar saltos */
}

.announcement-content {
  position: absolute;
  width: 100%;
  left: 0;
}

.top-announcement-bar p {
  margin: 0;
  font-weight: 600;
  letter-spacing: 0.2px;
  white-space: nowrap;
}

@media (max-width: 768px) {
  .top-announcement-bar {
    font-size: 12px;
    padding: 6px 0;
  }

  .announcement-container {
    height: 18px;
  }
}
