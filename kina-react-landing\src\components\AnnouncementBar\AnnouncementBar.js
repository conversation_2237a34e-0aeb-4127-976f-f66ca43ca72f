import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './AnnouncementBar.css';

const AnnouncementBar = () => {
  const announcements = [
    "Frete grátis acima de R$ 50 | Peça agora pelo iFood",
    "Cadastre-se e ganhe 5% OFF na primeira compra*, Cupom FIRST5",
    "Combinados a partir de R$ 39,90 | Delivery rápido para Fortaleza"
  ];
  
  const [currentIndex, setCurrentIndex] = useState(0);
  
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % announcements.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [announcements.length]);
  
  return (
    <div className="top-announcement-bar">
      <div className="announcement-container">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="announcement-content"
          >
            <p>{announcements[currentIndex]}</p>
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AnnouncementBar;
