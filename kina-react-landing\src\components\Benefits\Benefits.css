.benefits {
  background-color: var(--gray-color);
  position: relative;
  z-index: 1;
  padding: 80px 0;
}

.benefits::before {
  content: '';
  position: absolute;
  top: -50px;
  left: 0;
  width: 100%;
  height: 50px;
  background: linear-gradient(to bottom right, transparent 49%, var(--gray-color) 50%);
}

.benefits-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 25px;
  margin-top: 40px;
}

.benefit-item {
  text-align: center;
  padding: 30px 25px;
  background-color: var(--light-color);
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-top: 4px solid var(--primary-color);
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex: 1;
  min-width: 220px;
  max-width: 280px;
  margin: 0 5px;
}

.benefit-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 20px;
  display: inline-block;
  background-color: rgba(255, 204, 0, 0.1);
  width: 80px;
  height: 80px;
  line-height: 80px;
  border-radius: 50%;
  margin: 0 auto 20px;
}

.benefit-title {
  font-size: 1.4rem;
  margin-bottom: 15px;
  color: var(--dark-color);
  font-weight: 700;
}

.benefit-text {
  font-size: 1rem;
  color: #555;
  line-height: 1.6;
  flex-grow: 1;
}

@media (max-width: 768px) {
  .benefits {
    padding: 60px 0;
  }
  
  .benefit-item {
    min-width: 100%;
    max-width: 100%;
  }
  
  .benefit-icon {
    width: 70px;
    height: 70px;
    line-height: 70px;
    font-size: 2.5rem;
  }
  
  .benefit-title {
    font-size: 1.2rem;
  }
}
