import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './Benefits.css';

const benefitsData = [
  {
    id: 1,
    icon: "🍣",
    title: "Qualidade Premium",
    text: "Ingredientes frescos e selecionados para garantir o melhor sabor"
  },
  {
    id: 2,
    icon: "🚚",
    title: "Entrega Rápida",
    text: "Seu pedido entregue em até 45 minutos ou a entrega é grátis"
  },
  {
    id: 3,
    icon: "💰",
    title: "Preço Justo",
    text: "Melhor custo-benefício da cidade, sem comprometer a qualidade"
  },
  {
    id: 4,
    icon: "🌟",
    title: "Experiência Única",
    text: "Sabores autênticos da culinária japonesa e asiática"
  }
];

const BenefitItem = ({ benefit, index }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  return (
    <motion.div 
      className="benefit-item"
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.2 }}
      whileHover={{ y: -10, transition: { duration: 0.2 } }}
    >
      <motion.div 
        className="benefit-icon"
        initial={{ scale: 0 }}
        animate={inView ? { scale: 1, rotate: [0, 10, -10, 0] } : { scale: 0 }}
        transition={{ duration: 0.5, delay: index * 0.2 + 0.3 }}
      >
        {benefit.icon}
      </motion.div>
      <h3 className="benefit-title">{benefit.title}</h3>
      <p className="benefit-text">{benefit.text}</p>
    </motion.div>
  );
};

const Benefits = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  return (
    <section className="benefits section" id="benefits">
      <div className="container">
        <motion.h2 
          className="section-title"
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.5 }}
        >
          Por que pedir no Kina?
        </motion.h2>
        
        <div className="benefits-grid">
          {benefitsData.map((benefit, index) => (
            <BenefitItem key={benefit.id} benefit={benefit} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Benefits;
