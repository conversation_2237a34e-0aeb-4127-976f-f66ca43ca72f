.faq {
  background-color: var(--gray-color);
  position: relative;
  padding: 80px 0;
}

.faq-container {
  max-width: 900px;
  margin: 40px auto 0;
}

.faq-item {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
  background-color: var(--light-color);
}

.faq-item:hover {
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.12);
}

.faq-question {
  background-color: var(--light-color);
  padding: 20px 25px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.1rem;
  transition: background-color 0.3s ease;
  border-left: 4px solid var(--primary-color);
  position: relative;
}

.faq-question:after {
  content: '+';
  font-size: 1.8rem;
  color: var(--primary-color);
  font-weight: 700;
  transition: transform 0.3s ease;
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
}

.faq-answer {
  padding: 0 25px;
  overflow: hidden;
  background-color: var(--light-color);
  line-height: 1.7;
  color: #444;
  font-size: 1.05rem;
}

.faq-answer p {
  padding: 25px 0;
  margin: 0;
}

.faq-item.active .faq-question {
  background-color: #f9f9f9;
}

.faq-item.active .faq-question:after {
  content: '−';
  transform: translateY(-50%) rotate(180deg);
}

@media (max-width: 768px) {
  .faq {
    padding: 60px 0;
  }
  
  .faq-question {
    font-size: 1rem;
    padding: 15px 20px;
  }
  
  .faq-question:after {
    font-size: 1.5rem;
    right: 20px;
  }
  
  .faq-answer {
    padding: 0 20px;
    font-size: 0.95rem;
  }
  
  .faq-answer p {
    padding: 20px 0;
  }
}
