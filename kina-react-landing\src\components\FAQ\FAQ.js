import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './FAQ.css';

const faqData = [
  {
    id: 1,
    question: "Quais tipos de comida o Kina oferece?",
    answer: "O Kina Restaurante é especializado em culinária japonesa e asiática, oferecendo uma variedade de pratos como sushi, sashi<PERSON>, temaki, yakisoba, combinados e pratos quentes. Também temos opções de entradas, bebidas e sobremesas típicas."
  },
  {
    id: 2,
    question: "Qual o horário de funcionamento?",
    answer: "Nossas unidades funcionam de segunda a sábado, das 11h às 22h, e aos domingos das 11h às 21h. O serviço de delivery está disponível todos os dias, das 11h às 22h."
  },
  {
    id: 3,
    question: "Vocês cobram taxa de entrega?",
    answer: "Sim, cobramos taxa de entrega que varia de acordo com a região. No entanto, para pedidos acima de R$ 50,00, oferecemos frete grátis para determinadas regiões de Fortaleza. Consulte as condições no momento do pedido."
  },
  {
    id: 4,
    question: "Quais formas de pagamento são aceitas?",
    answer: "Aceitamos pagamentos em dinheiro, cartões de crédito e débito, PIX e vale-refeição (Sodexo, Alelo, VR e Ticket)."
  },
  {
    id: 5,
    question: "Vocês têm opções vegetarianas?",
    answer: "Sim! Temos diversas opções vegetarianas em nosso cardápio, incluindo temakis, hossomakis e uramakis com ingredientes como manga, abacate, pepino e cream cheese."
  },
  {
    id: 6,
    question: "Como faço para pedir pelo iFood?",
    answer: "É simples! Basta acessar o aplicativo ou site do iFood, buscar por 'Kina Restaurante' ou selecionar a unidade mais próxima de você, escolher seus pratos favoritos e finalizar o pedido. Você também pode clicar nos botões 'Pedir no iFood' em nosso site para ser direcionado diretamente para nossa página no iFood."
  }
];

const FAQItem = ({ item, isOpen, toggleOpen }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  return (
    <motion.div 
      className={`faq-item ${isOpen ? 'active' : ''}`}
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.4 }}
    >
      <div className="faq-question" onClick={toggleOpen}>
        {item.question}
      </div>
      <AnimatePresence>
        {isOpen && (
          <motion.div 
            className="faq-answer"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <p>{item.answer}</p>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

const FAQ = () => {
  const [openItems, setOpenItems] = useState({});
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  const toggleItem = (id) => {
    setOpenItems(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };
  
  return (
    <section className="section faq" id="faq">
      <div className="container">
        <motion.h2 
          className="section-title"
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.5 }}
        >
          Perguntas Frequentes
        </motion.h2>
        
        <div className="faq-container">
          {faqData.map((item) => (
            <FAQItem 
              key={item.id} 
              item={item} 
              isOpen={openItems[item.id] || false}
              toggleOpen={() => toggleItem(item.id)}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQ;
