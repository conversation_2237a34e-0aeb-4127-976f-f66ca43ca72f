import React from 'react';
import { motion } from 'framer-motion';
import './FloatingCTA.css';

const FloatingCTA = () => {
  return (
    <motion.div 
      className="floating-cta"
      initial={{ opacity: 0, y: 100 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        type: "spring", 
        stiffness: 260, 
        damping: 20,
        delay: 1.5
      }}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
    >
      <a 
        href="https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703" 
        target="_blank" 
        rel="noopener noreferrer"
        className="btn-cta"
      >
        Pedir no iFood
      </a>
    </motion.div>
  );
};

export default FloatingCTA;
