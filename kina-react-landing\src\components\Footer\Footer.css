.footer {
  background-color: var(--dark-color);
  color: var(--light-color);
  padding: 70px 0 30px;
  position: relative;
}

.footer:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 50px;
  margin-bottom: 40px;
}

.footer-info h3 {
  font-size: 1.5rem;
  margin-bottom: 25px;
  color: var(--primary-color);
  font-weight: 700;
  position: relative;
  padding-bottom: 12px;
}

.footer-info h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.footer-info p {
  margin-bottom: 15px;
  font-size: 1rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.8);
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--light-color);
  border-radius: 50%;
  transition: all 0.3s ease;
  text-decoration: none;
  font-size: 1.3rem;
}

.social-link svg {
  width: 20px;
  height: 20px;
}

.social-link:hover {
  background-color: var(--primary-color);
  color: var(--dark-color);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.footer-bottom {
  text-align: center;
  padding-top: 25px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.7);
}

@media (max-width: 768px) {
  .footer {
    padding: 50px 0 20px;
  }

  .footer-grid {
    gap: 30px;
  }

  .footer-info h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
  }

  .social-link {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }

  .social-link svg {
    width: 18px;
    height: 18px;
  }
}
