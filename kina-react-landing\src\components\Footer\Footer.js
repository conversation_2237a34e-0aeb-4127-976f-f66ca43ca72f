import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { FaInstagram, FaFacebookF, FaYoutube, FaWhatsapp } from 'react-icons/fa';
import './Footer.css';

const Footer = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const currentYear = new Date().getFullYear();

  return (
    <footer className="footer" ref={ref}>
      <div className="container">
        <motion.div
          className="footer-grid"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.5 }}
        >
          <div className="footer-info">
            <h3>Kina Restaurante</h3>
            <p>Comida japonesa e asiática de qualidade para toda Fortaleza.</p>
            <div className="social-links">
              <motion.a
                href="https://www.instagram.com/kinarestaurante/"
                target="_blank"
                rel="noopener noreferrer"
                className="social-link"
                whileHover={{ scale: 1.2, rotate: 10 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Instagram"
              >
                <FaInstagram />
              </motion.a>
              <motion.a
                href="#"
                className="social-link"
                whileHover={{ scale: 1.2, rotate: 10 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Facebook"
              >
                <FaFacebookF />
              </motion.a>
              <motion.a
                href="#"
                className="social-link"
                whileHover={{ scale: 1.2, rotate: 10 }}
                whileTap={{ scale: 0.9 }}
                aria-label="YouTube"
              >
                <FaYoutube />
              </motion.a>
              <motion.a
                href="#"
                className="social-link"
                whileHover={{ scale: 1.2, rotate: 10 }}
                whileTap={{ scale: 0.9 }}
                aria-label="WhatsApp"
              >
                <FaWhatsapp />
              </motion.a>
            </div>
          </div>

          <div className="footer-info">
            <h3>Horário de Funcionamento</h3>
            <p>Segunda a Sábado: 11h às 22h</p>
            <p>Domingo: 11h às 21h</p>
            <p>Delivery: Todos os dias das 11h às 22h</p>
          </div>

          <div className="footer-info">
            <h3>Contato</h3>
            <p>📞 (85) 3000-0000</p>
            <p>✉️ <EMAIL></p>
            <p>📱 WhatsApp: (85) 99999-9999</p>
          </div>
        </motion.div>

        <motion.div
          className="footer-bottom"
          initial={{ opacity: 0 }}
          animate={inView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <p>&copy; {currentYear} Kina Restaurante. Todos os direitos reservados.</p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
