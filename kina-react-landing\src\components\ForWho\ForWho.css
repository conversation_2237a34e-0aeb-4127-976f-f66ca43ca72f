.for-who {
  background-color: var(--light-color);
  position: relative;
  overflow: hidden;
  padding: 80px 0;
}

.for-who-container {
  background-color: var(--primary-color);
  padding: 50px 40px;
  border-radius: 15px;
  color: var(--dark-color);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(0, 0, 0, 0.05);
  max-width: 900px;
  margin: 0 auto;
}

.for-who-container:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(50%, -50%);
  z-index: -1;
}

.for-who-container:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200px;
  height: 200px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  transform: translate(-30%, 30%);
  z-index: -1;
}

.for-who-title {
  font-size: 2rem;
  margin-bottom: 30px;
  text-align: center;
  font-weight: 700;
  color: var(--dark-color);
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.for-who-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--secondary-color);
}

.for-who-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.for-who-item {
  margin-bottom: 20px;
  padding-left: 40px;
  position: relative;
  font-size: 1.2rem;
  line-height: 1.5;
  font-weight: 500;
}

.for-who-item:last-child {
  margin-bottom: 0;
}

.for-who-item:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--secondary-color);
  font-weight: bold;
  font-size: 1.4rem;
}

@media (max-width: 768px) {
  .for-who-container {
    padding: 30px 25px;
  }
  
  .for-who-title {
    font-size: 1.6rem;
    margin-bottom: 20px;
  }
  
  .for-who-item {
    font-size: 1.1rem;
    padding-left: 30px;
  }
  
  .for-who-item:before {
    font-size: 1.2rem;
  }
}
