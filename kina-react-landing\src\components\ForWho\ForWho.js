import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './ForWho.css';

const forWhoItems = [
  "Ama comida japonesa, mas quer variedade asiática",
  "Quer comer bem sem gastar muito",
  "Gosta de pedir delivery com agilidade e sabor",
  "Busca opções para compartilhar com amigos e família",
  "Valoriza qualidade e frescor nos ingredientes"
];

const ForWho = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.5,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.3 }
    }
  };
  
  return (
    <section className="section for-who" id="for-who">
      <div className="container">
        <motion.h2 
          className="section-title"
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.5 }}
        >
          Para Quem é o Kina?
        </motion.h2>
        
        <motion.div 
          className="for-who-container"
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          <motion.h3 
            className="for-who-title"
            initial={{ opacity: 0, y: 20 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Ideal para quem...
          </motion.h3>
          
          <ul className="for-who-list">
            {forWhoItems.map((item, index) => (
              <motion.li 
                key={index} 
                className="for-who-item"
                variants={itemVariants}
                custom={index}
              >
                {item}
              </motion.li>
            ))}
          </ul>
        </motion.div>
      </div>
    </section>
  );
};

export default ForWho;
