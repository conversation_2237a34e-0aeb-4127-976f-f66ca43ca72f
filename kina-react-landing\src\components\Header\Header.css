.header {
  background-color: var(--light-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 32px; /* Ajustado para ficar abaixo da barra de anúncio em mobile */
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 10px 0; /* Reduzido para ser mais compacto */
  height: auto; /* Auto height para melhor responsividade */
  min-height: 60px; /* Reduzido para ser mais compacto */
  transition: all 0.3s ease;
}

.header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  padding: 10px 0;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.logo-container {
  display: flex;
  align-items: center;
  height: 100%;
}

.header-cta {
  padding: 8px 20px; /* Reduzido para ser mais compacto */
  font-size: 0.9rem;
  font-weight: 700;
  letter-spacing: 0.5px;
}

@media (max-width: 768px) {
  .header {
    height: auto;
    padding: 25px 0; /* Ajustado para telas menores, mas ainda com espaço extra */
    top: 32px; /* Ajustado para telas médias */
  }

  .header-cta {
    padding: 8px 15px;
    font-size: 0.85rem;
  }
}

@media (max-width: 576px) {
  .header {
    top: 28px; /* Ajustado para telas pequenas */
    padding: 20px 0;
  }

  .header-cta {
    padding: 8px 15px;
    font-size: 0.8rem;
  }
}
