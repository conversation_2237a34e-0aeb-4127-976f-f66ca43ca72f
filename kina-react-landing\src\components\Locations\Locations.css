.locations {
  background-color: var(--light-color);
  position: relative;
  padding: 80px 0;
}

.locations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.location-item {
  background-color: var(--light-color);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.location-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.location-image-container {
  overflow: hidden;
  height: 200px;
}

.location-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.location-content {
  padding: 25px;
}

.location-title {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: var(--dark-color);
  font-weight: 700;
  position: relative;
  padding-bottom: 10px;
}

.location-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--primary-color);
}

.location-address {
  margin-bottom: 20px;
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
}

.location-cta {
  padding: 10px 20px;
  font-size: 0.9rem;
  display: inline-block;
}

@media (max-width: 992px) {
  .locations-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .locations {
    padding: 60px 0;
  }
  
  .locations-grid {
    grid-template-columns: 1fr;
  }
  
  .location-title {
    font-size: 1.3rem;
  }
  
  .location-address {
    font-size: 0.95rem;
  }
}
