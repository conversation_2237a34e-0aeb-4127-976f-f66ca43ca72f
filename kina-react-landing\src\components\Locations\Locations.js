import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './Locations.css';

const locationsData = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    address: "Av. <PERSON>, 85 - <PERSON><PERSON>\nShopping Iguatemi - Praça de Alimentação",
    image: "https://via.placeholder.com/400x300",
    ifoodUrl: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703"
  },
  {
    id: 2,
    name: "<PERSON><PERSON>se<PERSON>",
    address: "Av. <PERSON>, 3131 - Aldeota\nShopping Del Paseo - Praça de Alimentação",
    image: "https://via.placeholder.com/400x300",
    ifoodUrl: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-del-paseo-aldeota/053acccc-102c-4b47-b6df-b3ddc8d6c0f6"
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    address: "Rua <PERSON>em<PERSON>, 1500 - Papicu\nShopping RioMar - Praça de Alimentação",
    image: "https://via.placeholder.com/400x300",
    ifoodUrl: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-riomar-papicu/7b3ec6a1-feef-4f9c-b9f9-8e4d8c9c7f8a"
  }
];

const LocationItem = ({ location, index }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  return (
    <motion.div 
      className="location-item"
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.2 }}
    >
      <motion.div 
        className="location-image-container"
        whileHover={{ scale: 1.03 }}
        transition={{ duration: 0.3 }}
      >
        <img src={location.image} alt={location.name} className="location-img" />
      </motion.div>
      <div className="location-content">
        <h3 className="location-title">{location.name}</h3>
        <p className="location-address">{location.address.split('\\n').map((line, i) => (
          <React.Fragment key={i}>
            {line}
            {i < location.address.split('\\n').length - 1 && <br />}
          </React.Fragment>
        ))}</p>
        <motion.a 
          href={location.ifoodUrl} 
          target="_blank" 
          rel="noopener noreferrer"
          className="btn-cta location-cta"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Pedir desta unidade
        </motion.a>
      </div>
    </motion.div>
  );
};

const Locations = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  return (
    <section className="section locations" id="locations">
      <div className="container">
        <motion.h2 
          className="section-title"
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.5 }}
        >
          Nossas Unidades
        </motion.h2>
        
        <div className="locations-grid">
          {locationsData.map((location, index) => (
            <LocationItem key={location.id} location={location} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Locations;
