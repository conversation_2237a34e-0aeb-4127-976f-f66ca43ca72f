.carousel-wrapper {
  position: relative;
  margin: 50px 0 30px;
  width: 100%;
  overflow: hidden;
  padding: 0 40px; /* Aumentado para dar mais espaço para os botões de navegação */
  box-sizing: border-box; /* Garantir que o padding não aumente a largura total */
  max-width: 1200px; /* Limitar a largura máxima para controlar melhor o layout */
  margin-left: auto;
  margin-right: auto;
}

.carousel {
  overflow-x: hidden;
  cursor: grab;
  padding-bottom: 30px; /* Espaço para os dots */
}

.carousel:active {
  cursor: grabbing;
}

.carousel-container {
  display: flex;
  gap: 20px; /* Aumentado para garantir espaçamento adequado */
  padding: 15px 0;
  width: 100%;
}

.carousel-item {
  width: calc(25% - 15px); /* Exatamente 25% da largura menos o espaçamento */
  min-width: 220px; /* Tamanho mínimo para telas pequenas */
  max-width: 260px; /* Tamanho máximo para telas grandes */
  background-color: var(--light-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.carousel-img {
  height: 160px; /* Reduzido para melhor proporção em mobile */
  object-fit: cover;
  width: 100%;
  border-bottom: 3px solid var(--primary-color);
}

.carousel-content {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.carousel-title {
  font-size: 1.1rem; /* Reduzido para melhor visualização em mobile */
  margin-bottom: 6px;
  color: var(--dark-color);
  font-weight: 700;
  position: relative;
  padding-bottom: 8px;
  text-align: center;
}

.carousel-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}

.carousel-price {
  font-size: 1.25rem;
  font-weight: 800;
  color: var(--secondary-color);
  margin-bottom: 8px;
  text-align: center;
}

.carousel-description {
  font-size: 0.9rem;
  margin-bottom: 12px;
  color: #555;
  line-height: 1.4;
  flex-grow: 1;
  text-align: center;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Limita a 2 linhas */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.carousel-button-container {
  text-align: center;
  margin-top: 8px;
}

.carousel-button-container .btn-cta {
  padding: 8px 15px;
  font-size: 0.85rem;
  display: inline-block;
  width: 100%;
  border-radius: 6px;
}

.carousel-dots {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.carousel-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-dot:hover, .carousel-dot.active {
  background-color: var(--primary-color);
}

/* Botões de navegação */
.carousel-nav {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none; /* Permite clicar através dos botões quando não estão visíveis */
  z-index: 10;
  padding: 0;
  width: 100%;
}

.carousel-nav-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  pointer-events: auto; /* Permite clicar nos botões */
  color: var(--dark-color);
}

.carousel-nav-button:hover {
  background-color: var(--primary-color);
  color: var(--dark-color);
}

.carousel-nav-button.prev {
  left: 5px;
  position: absolute;
}

.carousel-nav-button.next {
  right: 5px;
  position: absolute;
}

/* Estilos específicos para telas muito pequenas (até 374px) */
@media (max-width: 374px) {
  .carousel-item {
    width: calc(100% - 40px); /* Ocupar quase toda a largura em telas muito pequenas */
    min-width: 200px;
  }

  .carousel-img {
    height: 140px;
  }

  .carousel-title {
    font-size: 1rem;
  }

  .carousel-price {
    font-size: 1.1rem;
  }

  .carousel-description {
    font-size: 0.85rem;
    -webkit-line-clamp: 2;
  }

  .carousel-button-container .btn-cta {
    padding: 7px 12px;
    font-size: 0.8rem;
  }
}

/* Mobile (375px a 767px) */
@media (min-width: 375px) and (max-width: 767px) {
  .carousel-item {
    width: calc(100% - 60px); /* Ocupar quase toda a largura em telas pequenas */
    min-width: 220px;
    max-width: 280px;
  }
}

/* Tablets (768px a 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  .carousel-item {
    width: calc(33.333% - 20px); /* Exatamente 1/3 da largura menos o espaçamento */
    min-width: 220px;
    max-width: 260px;
  }

  .carousel-img {
    height: 170px;
  }

  .carousel-title {
    font-size: 1.15rem;
  }

  .carousel-price {
    font-size: 1.3rem;
  }

  .carousel-description {
    font-size: 0.95rem;
    -webkit-line-clamp: 2;
  }

  .carousel-button-container .btn-cta {
    padding: 8px 18px;
    font-size: 0.9rem;
  }
}

/* Desktops (992px a 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
  .carousel-item {
    width: calc(33.333% - 20px); /* Exatamente 1/3 da largura menos o espaçamento */
    min-width: 240px;
    max-width: 280px;
  }

  .carousel-img {
    height: 180px;
  }

  .carousel-title {
    font-size: 1.2rem;
  }

  .carousel-price {
    font-size: 1.35rem;
  }

  .carousel-button-container .btn-cta {
    padding: 10px 20px;
    font-size: 0.95rem;
  }
}

/* Telas grandes (1200px e acima) */
@media (min-width: 1200px) {
  .carousel-item {
    width: calc(25% - 20px); /* Exatamente 1/4 da largura menos o espaçamento */
    min-width: 240px;
    max-width: 280px;
  }

  .carousel-img {
    height: 180px;
  }
}
