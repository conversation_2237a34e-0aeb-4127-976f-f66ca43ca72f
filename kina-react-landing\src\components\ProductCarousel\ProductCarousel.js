import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import './ProductCarousel.css';

// Dados completos dos produtos baseados no HTML original
const productData = [
  {
    id: 1,
    title: "Temaki Especial",
    price: 29.90,
    description: "Temaki recheado com salmão, cream cheese e cebolinha",
    image: "/images/temaki-especial.jpg",
    url: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=temaki-especial"
  },
  {
    id: 2,
    title: "Yakisoba de Carne",
    price: 39.90,
    description: "Macarrão oriental com legumes frescos e carne",
    image: "/images/yakisoba-carne.jpg",
    url: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=yakisoba-carne"
  },
  {
    id: 3,
    title: "Combinado 30 Peças",
    price: 89.90,
    description: "Seleção de sushis e sashimis para 2 pessoas",
    image: "/images/combinado-30.jpg",
    url: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=combinado-30-pecas"
  },
  {
    id: 4,
    title: "Hot Roll (10 unid)",
    price: 32.90,
    description: "Sushi empanado e frito com recheio especial",
    image: "/images/hot-roll.jpg",
    url: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=hot-roll"
  },
  {
    id: 5,
    title: "Sashimi de Salmão",
    price: 45.90,
    description: "10 fatias de salmão fresco fatiado na hora",
    image: "/images/sashimi-salmao.jpg",
    url: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=sashimi-salmao"
  },
  {
    id: 6,
    title: "Uramaki Philadelphia",
    price: 35.90,
    description: "8 unidades com salmão, cream cheese e cebolinha",
    image: "/images/uramaki-philadelphia.jpg",
    url: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=uramaki-philadelphia"
  },
  {
    id: 7,
    title: "Gyoza (5 unid)",
    price: 28.90,
    description: "Pastéis japoneses grelhados recheados com carne",
    image: "/images/gyoza.jpg",
    url: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=gyoza"
  },
  {
    id: 8,
    title: "Tempura de Camarão",
    price: 49.90,
    description: "Camarões empanados em massa leve e crocante",
    image: "/images/tempura-camarao.jpg",
    url: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=tempura-camarao"
  },
  {
    id: 9,
    title: "Hossomaki de Kani",
    price: 25.90,
    description: "8 unidades de sushi fino com kani e arroz",
    image: "/images/hossomaki-kani.jpg",
    url: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=hossomaki-kani"
  },
  {
    id: 10,
    title: "Niguiri de Salmão",
    price: 29.90,
    description: "6 unidades de bolinho de arroz com fatia de salmão",
    image: "/images/niguiri-salmao.jpg",
    url: "https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703?prato=niguiri-salmao"
  }
];

const ProductCard = ({ product, index }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <motion.div
      className="carousel-item"
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -10, transition: { duration: 0.2 } }}
    >
      <img
        src={product.image}
        alt={product.title}
        className="carousel-img"
        loading="lazy"
      />
      <div className="carousel-content">
        <h3 className="carousel-title">{product.title}</h3>
        <p className="carousel-price">R$ {product.price.toFixed(2)}</p>
        <p className="carousel-description">{product.description}</p>
        <div className="carousel-button-container">
          <motion.a
            href={product.url}
            target="_blank"
            rel="noopener noreferrer"
            className="btn-cta btn-cta-secondary"
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
          >
            Pedir agora
          </motion.a>
        </div>
      </div>
    </motion.div>
  );
};

const ProductCarousel = () => {
  const [width, setWidth] = useState(0);
  const [activeIndex, setActiveIndex] = useState(0);
  const carousel = useRef();
  const autoPlayRef = useRef();
  const intervalRef = useRef(null);

  // Função para calcular a largura do item com base no tamanho da tela
  const getItemWidth = () => {
    // Obter a largura do container
    const containerWidth = carousel.current ? carousel.current.offsetWidth : window.innerWidth;

    // Calcular a largura do item com base no tamanho da tela
    if (window.innerWidth >= 1200) {
      return Math.floor(containerWidth / 4) - 20; // 4 itens por linha em telas grandes
    } else if (window.innerWidth >= 992) {
      return Math.floor(containerWidth / 3) - 20; // 3 itens por linha em desktops
    } else if (window.innerWidth >= 768) {
      return Math.floor(containerWidth / 3) - 20; // 3 itens por linha em tablets
    } else if (window.innerWidth >= 375) {
      return Math.floor(containerWidth) - 60; // 1 item por linha em mobile
    } else {
      return Math.floor(containerWidth) - 40; // 1 item por linha em telas muito pequenas
    }
  };



  // Função para avançar para o próximo slide
  const nextSlide = useCallback(() => {
    if (carousel.current) {
      const itemWidth = getItemWidth();
      const gap = 20; // Ajustado para corresponder ao gap no CSS
      const totalItemWidth = itemWidth + gap;

      // Calcular o próximo índice, com loop
      const nextIndex = (activeIndex + 1) % productData.length;
      setActiveIndex(nextIndex);

      // Rolar para o próximo item
      carousel.current.scrollTo({
        left: nextIndex * totalItemWidth,
        behavior: 'smooth'
      });
    }
  }, [activeIndex]);

  // Configurar autoplay
  useEffect(() => {
    autoPlayRef.current = nextSlide;
  }, [nextSlide]);

  useEffect(() => {
    // Iniciar autoplay
    const play = () => {
      autoPlayRef.current();
    };

    // Definir intervalo de 5 segundos para autoplay
    intervalRef.current = setInterval(play, 5000);

    // Limpar intervalo quando o componente for desmontado
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    // Calcular a largura do carrossel para limitar o arrasto
    const updateWidth = () => {
      if (carousel.current) {
        // Obter a largura do item e o gap
        const itemWidth = getItemWidth();
        const gap = 20; // Ajustado para corresponder ao gap no CSS

        // Calcular a largura total do carrossel
        const totalWidth = productData.length * (itemWidth + gap);
        const visibleWidth = carousel.current.offsetWidth;

        // Garantir que o último card não seja cortado
        setWidth(totalWidth - visibleWidth + gap);
      }
    };

    // Atualizar a largura inicialmente
    updateWidth();

    // Atualizar a largura quando a janela for redimensionada
    window.addEventListener('resize', updateWidth);

    // Limpar o event listener quando o componente for desmontado
    return () => window.removeEventListener('resize', updateWidth);
  }, []);

  // Função para navegar para um slide específico
  const goToSlide = (index) => {
    if (carousel.current) {
      const itemWidth = getItemWidth();
      const gap = 20; // Ajustado para corresponder ao gap no CSS
      const totalItemWidth = itemWidth + gap;

      setActiveIndex(index);

      carousel.current.scrollTo({
        left: index * totalItemWidth,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="carousel-wrapper">
      <motion.div ref={carousel} className="carousel">
        <motion.div
          className="carousel-container"
          drag="x"
          dragConstraints={{ right: 0, left: -width }}
          whileTap={{ cursor: 'grabbing' }}
          onDragEnd={(e, info) => {
            // Detectar direção do arrasto
            if (info.offset.x < -50 && activeIndex < productData.length - 1) {
              goToSlide(activeIndex + 1);
            } else if (info.offset.x > 50 && activeIndex > 0) {
              goToSlide(activeIndex - 1);
            }
          }}
        >
          {productData.map((product, index) => (
            <ProductCard key={product.id} product={product} index={index} />
          ))}
        </motion.div>
      </motion.div>

      <div className="carousel-dots">
        {productData.map((_, index) => (
          <motion.button
            key={index}
            className={`carousel-dot ${index === activeIndex ? 'active' : ''}`}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => goToSlide(index)}
          />
        ))}
      </div>

      {/* Botões de navegação */}
      <div className="carousel-nav">
        <button
          className="carousel-nav-button prev"
          onClick={() => {
            const prevIndex = activeIndex === 0 ? productData.length - 1 : activeIndex - 1;
            goToSlide(prevIndex);
          }}
        >
          &#10094;
        </button>
        <button
          className="carousel-nav-button next"
          onClick={() => {
            const nextIndex = (activeIndex + 1) % productData.length;
            goToSlide(nextIndex);
          }}
        >
          &#10095;
        </button>
      </div>
    </div>
  );
};

export default ProductCarousel;
