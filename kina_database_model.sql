-- Criação do banco de dados para o Kina Restaurante
CREATE DATABASE IF NOT EXISTS kina_restaurante;
USE kina_restaurante;

-- Tabela de Unidades (Filiais)
CREATE TABLE IF NOT EXISTS unidades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    endereco VARCHAR(255) NOT NULL,
    bairro VARCHAR(100) NOT NULL,
    cidade VARCHAR(100) NOT NULL,
    estado CHAR(2) NOT NULL,
    cep VARCHAR(10),
    telefone VARCHAR(20),
    horario_funcionamento VARCHAR(255),
    ifood_id VARCHAR(100) NOT NULL COMMENT 'ID da unidade no iFood',
    ifood_url VARCHAR(255) NOT NULL COMMENT 'URL completa da unidade no iFood',
    imagem_url VARCHAR(255) COMMENT 'URL da imagem da unidade',
    ativo BOOLEAN DEFAULT TRUE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabela de Categorias de Produtos
CREATE TABLE IF NOT EXISTS categorias (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    descricao TEXT,
    ordem INT DEFAULT 0 COMMENT 'Ordem de exibição da categoria',
    ativo BOOLEAN DEFAULT TRUE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabela de Produtos
CREATE TABLE IF NOT EXISTS produtos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    categoria_id INT NOT NULL,
    nome VARCHAR(100) NOT NULL,
    descricao TEXT,
    preco DECIMAL(10,2) NOT NULL,
    imagem_url VARCHAR(255) COMMENT 'URL da imagem do produto',
    imagem_local VARCHAR(255) COMMENT 'Caminho local da imagem do produto',
    ifood_id VARCHAR(100) COMMENT 'ID do produto no iFood',
    ifood_url VARCHAR(255) COMMENT 'URL completa do produto no iFood',
    destaque BOOLEAN DEFAULT FALSE COMMENT 'Se o produto deve ser destacado no carrossel',
    ordem_destaque INT DEFAULT 0 COMMENT 'Ordem de exibição no carrossel',
    ativo BOOLEAN DEFAULT TRUE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (categoria_id) REFERENCES categorias(id)
);

-- Tabela de Produtos por Unidade (para preços e disponibilidade diferentes por unidade)
CREATE TABLE IF NOT EXISTS produtos_unidades (
    produto_id INT NOT NULL,
    unidade_id INT NOT NULL,
    preco DECIMAL(10,2) COMMENT 'Preço específico para esta unidade (se diferente)',
    disponivel BOOLEAN DEFAULT TRUE,
    ifood_id VARCHAR(100) COMMENT 'ID do produto no iFood para esta unidade específica',
    ifood_url VARCHAR(255) COMMENT 'URL completa do produto no iFood para esta unidade',
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (produto_id, unidade_id),
    FOREIGN KEY (produto_id) REFERENCES produtos(id),
    FOREIGN KEY (unidade_id) REFERENCES unidades(id)
);

-- Tabela de Benefícios (Por que pedir no Kina?)
CREATE TABLE IF NOT EXISTS beneficios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titulo VARCHAR(100) NOT NULL,
    descricao TEXT NOT NULL,
    icone VARCHAR(50) COMMENT 'Emoji ou código do ícone',
    ordem INT DEFAULT 0 COMMENT 'Ordem de exibição do benefício',
    ativo BOOLEAN DEFAULT TRUE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabela de FAQ (Perguntas Frequentes)
CREATE TABLE IF NOT EXISTS faq (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pergunta VARCHAR(255) NOT NULL,
    resposta TEXT NOT NULL,
    ordem INT DEFAULT 0 COMMENT 'Ordem de exibição da pergunta',
    ativo BOOLEAN DEFAULT TRUE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabela de Configurações do Site
CREATE TABLE IF NOT EXISTS configuracoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chave VARCHAR(100) NOT NULL UNIQUE,
    valor TEXT,
    descricao VARCHAR(255),
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Inserção de dados iniciais para as unidades
INSERT INTO unidades (nome, endereco, bairro, cidade, estado, ifood_id, ifood_url) VALUES
('Kina Iguatemi', 'Av. Washington Soares, 85 - Shopping Iguatemi - Praça de Alimentação', 'Edson Queiroz', 'Fortaleza', 'CE', '9f2d515f-e09d-4b23-8929-632de774c703', 'https://www.ifood.com.br/delivery/fortaleza-ce/kina-iguatemi-edson-queiroz/9f2d515f-e09d-4b23-8929-632de774c703'),
('Kina Del Paseo', 'Av. Santos Dumont, 3131 - Shopping Del Paseo - Praça de Alimentação', 'Aldeota', 'Fortaleza', 'CE', '', 'https://www.ifood.com.br/delivery/fortaleza-ce/kina-del-paseo-aldeota/'),
('Kina Benfica', 'Av. Carapinima, 2200', 'Benfica', 'Fortaleza', 'CE', '', 'https://www.ifood.com.br/delivery/fortaleza-ce/kina-benfica/');

-- Inserção de dados iniciais para as categorias
INSERT INTO categorias (nome, descricao, ordem) VALUES
('Entradas', 'Deliciosas opções para começar sua refeição', 1),
('Temakis', 'Cones de alga recheados com arroz e peixe fresco', 2),
('Combinados', 'Seleções variadas de sushi e sashimi', 3),
('Sushi', 'Peças tradicionais da culinária japonesa', 4),
('Sashimi', 'Fatias frescas de peixe', 5),
('Pratos Quentes', 'Opções quentes da culinária asiática', 6);

-- Inserção de dados iniciais para os produtos (com IDs fictícios do iFood)
INSERT INTO produtos (categoria_id, nome, descricao, preco, destaque, ordem_destaque) VALUES
((SELECT id FROM categorias WHERE nome = 'Temakis'), 'Temaki Especial', 'Temaki recheado com salmão, cream cheese e cebolinha', 29.90, TRUE, 1),
((SELECT id FROM categorias WHERE nome = 'Pratos Quentes'), 'Yakisoba de Carne', 'Macarrão oriental com legumes frescos e carne', 39.90, TRUE, 2),
((SELECT id FROM categorias WHERE nome = 'Combinados'), 'Combinado 30 Peças', 'Seleção de sushis e sashimis para 2 pessoas', 89.90, TRUE, 3),
((SELECT id FROM categorias WHERE nome = 'Sushi'), 'Hot Roll (10 unid)', 'Sushi empanado e frito com recheio especial', 32.90, TRUE, 4),
((SELECT id FROM categorias WHERE nome = 'Sashimi'), 'Sashimi de Salmão', '10 fatias de salmão fresco fatiado na hora', 45.90, TRUE, 5),
((SELECT id FROM categorias WHERE nome = 'Sushi'), 'Uramaki Philadelphia', '8 unidades com salmão, cream cheese e cebolinha', 35.90, TRUE, 6),
((SELECT id FROM categorias WHERE nome = 'Entradas'), 'Gyoza (5 unid)', 'Pastéis japoneses grelhados recheados com carne', 28.90, TRUE, 7),
((SELECT id FROM categorias WHERE nome = 'Entradas'), 'Tempura de Camarão', 'Camarões empanados em massa leve e crocante', 49.90, TRUE, 8),
((SELECT id FROM categorias WHERE nome = 'Sushi'), 'Hossomaki de Kani', '8 unidades de sushi fino com kani e arroz', 25.90, TRUE, 9),
((SELECT id FROM categorias WHERE nome = 'Sushi'), 'Niguiri de Salmão', '6 unidades de bolinho de arroz com fatia de salmão', 29.90, TRUE, 10);

-- Inserção de dados iniciais para os benefícios
INSERT INTO beneficios (titulo, descricao, icone, ordem) VALUES
('Sabor Autêntico', 'Receitas tradicionais japonesas com toque cearense. Nosso sushi é preparado por chefs especializados que dominam a arte da culinária oriental.', '🔥', 1),
('Delivery Express', 'Entrega ultrarrápida para sua comida chegar quentinha e no ponto. Nosso tempo médio de entrega é de apenas 35 minutos para Fortaleza.', '⚡', 2),
('Peixe Fresco Diário', 'Salmão e peixes selecionados recebidos diariamente. Trabalhamos apenas com fornecedores certificados que garantem a máxima qualidade.', '🐟', 3),
('Melhor Custo-Benefício', 'Porções generosas a preços justos. Nossos combinados são pensados para satisfazer seu apetite sem pesar no bolso. Experimente e comprove!', '💰', 4);

-- Inserção de dados iniciais para o FAQ
INSERT INTO faq (pergunta, resposta, ordem) VALUES
('Quais tipos de comida o Kina oferece?', 'O Kina Restaurante é especializado em culinária japonesa e asiática, oferecendo uma variedade de pratos como sushi, sashimi, temaki, yakisoba, combinados e pratos quentes. Também temos opções de entradas, bebidas e sobremesas típicas.', 1),
('Posso agendar pedidos para uma data específica?', 'Sim! Você pode agendar seu pedido pelo iFood para o dia e horário que preferir. Basta selecionar a opção "Agendar" ao finalizar seu pedido no aplicativo.', 2),
('O Kina entrega em qual região de Fortaleza?', 'Atendemos diversas regiões de Fortaleza, com entrega disponível para bairros como Aldeota, Meireles, Varjota, Cocó, Edson Queiroz, Benfica, Fátima, entre outros. A disponibilidade e taxa de entrega podem ser verificadas diretamente no iFood.', 3),
('Como encontro o Kina no iFood?', 'Você pode encontrar o Kina no iFood buscando por "Kina Restaurante" ou "Kina Japonês" na barra de pesquisa. Também pode acessar diretamente através do link em nosso site ou clicando nos botões de pedido desta página.', 4),
('O Kina tem opções vegetarianas?', 'Sim! Oferecemos diversas opções vegetarianas, como temakis, hossomakis e uramakis de legumes, além de yakisoba vegetariano e outras opções. Todas estão identificadas em nosso cardápio no iFood.', 5);

-- Inserção de dados iniciais para as configurações
INSERT INTO configuracoes (chave, valor, descricao) VALUES
('site_titulo', 'Kina Restaurante - Comida Japonesa em Fortaleza', 'Título do site'),
('site_descricao', 'O melhor da culinária japonesa e asiática em Fortaleza. Peça agora pelo iFood!', 'Descrição do site para SEO'),
('cor_primaria', '#FFCC00', 'Cor primária do site (amarelo)'),
('cor_secundaria', '#E60000', 'Cor secundária do site (vermelho)'),
('telefone_contato', '(85) 3000-0000', 'Telefone principal de contato'),
('email_contato', '<EMAIL>', 'Email principal de contato'),
('whatsapp', '(85) 99999-9999', 'Número de WhatsApp para contato'),
('instagram', 'https://www.instagram.com/kinarestaurante/', 'Link do Instagram');
