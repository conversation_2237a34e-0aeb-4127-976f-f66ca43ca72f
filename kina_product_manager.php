<?php
/**
 * Kina Restaurante - Gerenciador de Produtos
 * 
 * Este script permite gerenciar os produtos do Kina Restaurante,
 * incluindo a atualização dos IDs dos produtos no iFood.
 */

// Configurações de conexão com o banco de dados
$config = [
    'host' => 'localhost',
    'dbname' => 'kina_restaurante',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4'
];

// Conexão com o banco de dados
try {
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ]);
} catch (PDOException $e) {
    die("Erro de conexão: " . $e->getMessage());
}

/**
 * Função para obter todos os produtos
 */
function getProdutos($pdo) {
    $stmt = $pdo->query("
        SELECT p.*, c.nome as categoria_nome
        FROM produtos p
        JOIN categorias c ON p.categoria_id = c.id
        ORDER BY p.destaque DESC, p.ordem_destaque ASC, p.nome ASC
    ");
    return $stmt->fetchAll();
}

/**
 * Função para obter todas as unidades
 */
function getUnidades($pdo) {
    $stmt = $pdo->query("SELECT * FROM unidades WHERE ativo = 1 ORDER BY nome ASC");
    return $stmt->fetchAll();
}

/**
 * Função para atualizar o ID do iFood de um produto
 */
function atualizarProdutoIFood($pdo, $produtoId, $ifoodId, $ifoodUrl) {
    $stmt = $pdo->prepare("
        UPDATE produtos 
        SET ifood_id = :ifood_id, ifood_url = :ifood_url 
        WHERE id = :id
    ");
    return $stmt->execute([
        'ifood_id' => $ifoodId,
        'ifood_url' => $ifoodUrl,
        'id' => $produtoId
    ]);
}

/**
 * Função para atualizar o ID do iFood de um produto em uma unidade específica
 */
function atualizarProdutoUnidadeIFood($pdo, $produtoId, $unidadeId, $ifoodId, $ifoodUrl) {
    // Verificar se o registro já existe
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM produtos_unidades 
        WHERE produto_id = :produto_id AND unidade_id = :unidade_id
    ");
    $stmt->execute([
        'produto_id' => $produtoId,
        'unidade_id' => $unidadeId
    ]);
    
    $existe = $stmt->fetchColumn() > 0;
    
    if ($existe) {
        // Atualizar registro existente
        $stmt = $pdo->prepare("
            UPDATE produtos_unidades 
            SET ifood_id = :ifood_id, ifood_url = :ifood_url 
            WHERE produto_id = :produto_id AND unidade_id = :unidade_id
        ");
    } else {
        // Inserir novo registro
        $stmt = $pdo->prepare("
            INSERT INTO produtos_unidades (produto_id, unidade_id, ifood_id, ifood_url)
            VALUES (:produto_id, :unidade_id, :ifood_id, :ifood_url)
        ");
    }
    
    return $stmt->execute([
        'produto_id' => $produtoId,
        'unidade_id' => $unidadeId,
        'ifood_id' => $ifoodId,
        'ifood_url' => $ifoodUrl
    ]);
}

/**
 * Função para gerar a URL completa do produto no iFood
 */
function gerarUrlIFood($unidadeUrl, $produtoId) {
    // Remover a barra final se existir
    $unidadeUrl = rtrim($unidadeUrl, '/');
    
    // Adicionar o parâmetro do produto
    return $unidadeUrl . '?prato=' . $produtoId;
}

// Processar o formulário de atualização
$mensagem = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['atualizar_produto'])) {
        $produtoId = $_POST['produto_id'];
        $ifoodId = $_POST['ifood_id'];
        $unidadeId = $_POST['unidade_id'];
        
        // Obter a URL base da unidade
        $stmt = $pdo->prepare("SELECT ifood_url FROM unidades WHERE id = :id");
        $stmt->execute(['id' => $unidadeId]);
        $unidade = $stmt->fetch();
        
        if ($unidade) {
            $ifoodUrl = gerarUrlIFood($unidade['ifood_url'], $ifoodId);
            
            // Atualizar o produto para a unidade específica
            if (atualizarProdutoUnidadeIFood($pdo, $produtoId, $unidadeId, $ifoodId, $ifoodUrl)) {
                $mensagem = "ID do produto no iFood atualizado com sucesso!";
            } else {
                $mensagem = "Erro ao atualizar o ID do produto.";
            }
        } else {
            $mensagem = "Unidade não encontrada.";
        }
    }
}

// Obter todos os produtos e unidades
$produtos = getProdutos($pdo);
$unidades = getUnidades($pdo);

// Cabeçalho HTML
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciador de Produtos - Kina Restaurante</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2 {
            color: #E60000;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #FFCC00;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #E60000;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #cc0000;
        }
        .alert {
            padding: 10px;
            background-color: #d4edda;
            color: #155724;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gerenciador de Produtos - Kina Restaurante</h1>
        
        <?php if ($mensagem): ?>
            <div class="alert"><?php echo $mensagem; ?></div>
        <?php endif; ?>
        
        <h2>Atualizar ID do Produto no iFood</h2>
        <form method="post">
            <div class="form-group">
                <label for="produto_id">Produto:</label>
                <select name="produto_id" id="produto_id" required>
                    <option value="">Selecione um produto</option>
                    <?php foreach ($produtos as $produto): ?>
                        <option value="<?php echo $produto['id']; ?>">
                            <?php echo $produto['nome']; ?> (<?php echo $produto['categoria_nome']; ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="unidade_id">Unidade:</label>
                <select name="unidade_id" id="unidade_id" required>
                    <option value="">Selecione uma unidade</option>
                    <?php foreach ($unidades as $unidade): ?>
                        <option value="<?php echo $unidade['id']; ?>">
                            <?php echo $unidade['nome']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="ifood_id">ID do Produto no iFood:</label>
                <input type="text" name="ifood_id" id="ifood_id" required 
                       placeholder="Ex: 20174c4a-7796-4eb3-b3bd-6037aa1fcab5">
                <small>Este é o ID que aparece na URL do produto no iFood após o parâmetro "?prato="</small>
            </div>
            
            <button type="submit" name="atualizar_produto">Atualizar Produto</button>
        </form>
        
        <h2>Lista de Produtos</h2>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Nome</th>
                    <th>Categoria</th>
                    <th>Preço</th>
                    <th>Destaque</th>
                    <th>ID no iFood</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($produtos as $produto): ?>
                    <tr>
                        <td><?php echo $produto['id']; ?></td>
                        <td><?php echo $produto['nome']; ?></td>
                        <td><?php echo $produto['categoria_nome']; ?></td>
                        <td>R$ <?php echo number_format($produto['preco'], 2, ',', '.'); ?></td>
                        <td><?php echo $produto['destaque'] ? 'Sim' : 'Não'; ?></td>
                        <td><?php echo $produto['ifood_id'] ?: 'Não definido'; ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</body>
</html>
