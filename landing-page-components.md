# Componentes Reutilizáveis para Landing Pages

Este documento contém componentes HTML, CSS e JavaScript prontos para uso em diferentes projetos de landing pages. Copie e adapte conforme necessário.

## Componentes de Navegação

### 1. Header Fixo com Logo e CTA

```html
<header class="header">
    <div class="container header-container">
        <div class="logo-container">
            <img src="logo.png" alt="Nome da Empresa" class="logo">
        </div>
        <a href="#cta-section" class="btn-cta header-cta">A<PERSON> Principal</a>
    </div>
</header>
```

```css
.header {
    background-color: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    padding: 15px 0;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    height: 40px;
    width: auto;
}

.header-cta {
    padding: 10px 20px;
    font-size: 0.9rem;
}
```

### 2. Barra de Anúncio <PERSON>zante

```html
<div class="announcement-bar">
    <div class="announcement-container">
        <div class="announcement-slider">
            <div class="announcement-slide">
                <p>Primeiro anúncio importante</p>
            </div>
            <div class="announcement-slide">
                <p>Segundo anúncio importante</p>
            </div>
            <div class="announcement-slide">
                <p>Terceiro anúncio importante</p>
            </div>
        </div>
    </div>
</div>
```

```css
.announcement-bar {
    background-color: #ffcc00;
    color: #000000;
    width: 100%;
    text-align: center;
    padding: 8px 0;
    font-size: 14px;
    position: relative;
    z-index: 2000;
    overflow: hidden;
}

.announcement-slider {
    display: flex;
    width: 300%;
    animation: slide 20s linear infinite;
}

.announcement-slide {
    width: 100%;
    flex-shrink: 0;
}

@keyframes slide {
    0% { transform: translateX(0); }
    28% { transform: translateX(0); }
    33% { transform: translateX(-33.33%); }
    61% { transform: translateX(-33.33%); }
    66% { transform: translateX(-66.66%); }
    94% { transform: translateX(-66.66%); }
    100% { transform: translateX(0); }
}
```

## Componentes de Hero Section

### 1. Hero com Imagem de Fundo

```html
<section class="hero">
    <div class="container">
        <h1 class="hero-title">Título Principal da Sua Landing Page</h1>
        <p class="hero-subtitle">Uma descrição convincente do seu produto ou serviço em uma ou duas frases.</p>
        <a href="#cta-section" class="btn-cta">Chamada para Ação</a>
    </div>
</section>
```

```css
.hero {
    background-color: #000000;
    color: #ffffff;
    padding: 120px 0 80px;
    text-align: center;
    background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('hero-bg.jpg');
    background-size: cover;
    background-position: center;
}

.hero-title {
    font-size: 2.8rem;
    margin-bottom: 20px;
    font-weight: 800;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 35px;
    font-weight: 300;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.btn-cta {
    display: inline-block;
    padding: 15px 30px;
    background-color: #e60000;
    color: #ffffff;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn-cta:hover {
    background-color: #cc0000;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(230, 0, 0, 0.4);
}
```

## Componentes de Carrossel

### 1. Carrossel de Produtos

```html
<div class="carousel-wrapper">
    <div class="carousel-controls">
        <button class="carousel-control prev" aria-label="Anterior">&lt;</button>
        <button class="carousel-control next" aria-label="Próximo">&gt;</button>
    </div>
    <div class="carousel">
        <div class="carousel-container">
            <!-- Item 1 -->
            <div class="carousel-item">
                <img src="produto1.jpg" alt="Produto 1" class="carousel-img">
                <div class="carousel-content">
                    <h3 class="carousel-title">Nome do Produto 1</h3>
                    <p class="carousel-price">R$ 99,90</p>
                    <p class="carousel-description">Breve descrição do produto.</p>
                    <a href="#" class="btn-cta btn-cta-secondary">Comprar</a>
                </div>
            </div>
            
            <!-- Adicione mais itens conforme necessário -->
        </div>
    </div>
</div>
```

```css
.carousel-wrapper {
    position: relative;
    margin: 50px 0 30px;
    width: 100%;
    overflow: hidden;
}

.carousel-controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    z-index: 10;
    padding: 0 10px;
}

.carousel-control {
    width: 40px;
    height: 40px;
    background-color: #ffcc00;
    color: #000000;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.carousel {
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none;
    padding-bottom: 15px;
    position: relative;
    scroll-behavior: smooth;
}

.carousel::-webkit-scrollbar {
    display: none;
}

.carousel-container {
    display: inline-flex;
    gap: 25px;
    padding: 15px 10px;
    transition: transform 0.5s ease;
}

.carousel-item {
    width: 280px;
    background-color: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    display: inline-block;
    white-space: normal;
    flex-shrink: 0;
}

.carousel-img {
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.carousel-content {
    padding: 20px;
    height: 220px;
    display: flex;
    flex-direction: column;
}

.carousel-title {
    font-size: 1.25rem;
    margin-bottom: 8px;
    font-weight: 700;
}

.carousel-price {
    font-size: 1.4rem;
    font-weight: 800;
    color: #e60000;
    margin-bottom: 12px;
}

.carousel-description {
    font-size: 1rem;
    margin-bottom: 20px;
    color: #555;
    line-height: 1.5;
    flex-grow: 1;
}
```

```javascript
document.addEventListener('DOMContentLoaded', function() {
    const carousel = document.querySelector('.carousel');
    const carouselContainer = document.querySelector('.carousel-container');
    const prevButton = document.querySelector('.carousel-control.prev');
    const nextButton = document.querySelector('.carousel-control.next');
    const itemWidth = 305; // Largura do item + gap
    let scrollAmount = 0;
    
    // Função para rolar para a esquerda
    function scrollLeft() {
        scrollAmount -= itemWidth;
        if (scrollAmount < 0) {
            scrollAmount = carouselContainer.scrollWidth - carousel.clientWidth;
        }
        carousel.scrollTo({
            left: scrollAmount,
            behavior: 'smooth'
        });
    }
    
    // Função para rolar para a direita
    function scrollRight() {
        scrollAmount += itemWidth;
        if (scrollAmount > carouselContainer.scrollWidth - carousel.clientWidth) {
            scrollAmount = 0;
        }
        carousel.scrollTo({
            left: scrollAmount,
            behavior: 'smooth'
        });
    }
    
    // Adicionar eventos de clique aos botões
    prevButton.addEventListener('click', scrollLeft);
    nextButton.addEventListener('click', scrollRight);
});
```

## Componentes de Benefícios

### 1. Grid de Benefícios com Ícones

```html
<section class="benefits">
    <div class="container">
        <h2 class="section-title">Nossos Benefícios</h2>
        <div class="benefits-grid">
            <!-- Benefício 1 -->
            <div class="benefit-item">
                <div class="benefit-icon">🔥</div>
                <h3 class="benefit-title">Título do Benefício 1</h3>
                <p class="benefit-text">Descrição detalhada do benefício e como ele ajuda o cliente.</p>
            </div>
            
            <!-- Adicione mais benefícios conforme necessário -->
        </div>
    </div>
</section>
```

```css
.benefits {
    background-color: #f5f5f5;
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2rem;
    position: relative;
}

.section-title:after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background-color: #ffcc00;
    margin: 15px auto 0;
}

.benefits-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.benefit-item {
    text-align: center;
    padding: 30px 25px;
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    flex: 1;
    min-width: 250px;
    max-width: 350px;
}

.benefit-icon {
    font-size: 3rem;
    color: #ffcc00;
    margin-bottom: 20px;
    display: inline-block;
    background-color: rgba(255, 204, 0, 0.1);
    width: 80px;
    height: 80px;
    line-height: 80px;
    border-radius: 50%;
    margin: 0 auto 20px;
}

.benefit-title {
    font-size: 1.4rem;
    margin-bottom: 15px;
    font-weight: 700;
}

.benefit-text {
    font-size: 1rem;
    color: #555;
    line-height: 1.6;
}
```

## Componentes de FAQ

### 1. Acordeão de Perguntas Frequentes

```html
<section class="faq">
    <div class="container">
        <h2 class="section-title">Perguntas Frequentes</h2>
        
        <!-- Pergunta 1 -->
        <div class="faq-item">
            <div class="faq-question">Primeira pergunta frequente?</div>
            <div class="faq-answer">
                <p>Resposta detalhada para a primeira pergunta frequente.</p>
            </div>
        </div>
        
        <!-- Adicione mais perguntas conforme necessário -->
    </div>
</section>
```

```css
.faq {
    background-color: #f5f5f5;
    padding: 80px 0;
}

.faq-item {
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
}

.faq-question {
    background-color: #ffffff;
    padding: 20px 25px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.1rem;
    transition: background-color 0.3s ease;
    border-left: 4px solid #ffcc00;
}

.faq-question:after {
    content: '+';
    font-size: 1.8rem;
    color: #ffcc00;
    font-weight: 700;
    transition: transform 0.3s ease;
}

.faq-answer {
    padding: 0 25px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s ease;
    background-color: #fff;
    line-height: 1.7;
    color: #444;
    font-size: 1.05rem;
}

.faq-item.active .faq-question {
    background-color: #f9f9f9;
}

.faq-item.active .faq-question:after {
    content: '-';
    transform: rotate(180deg);
}

.faq-item.active .faq-answer {
    padding: 25px;
    max-height: 1000px;
}
```

```javascript
document.querySelectorAll('.faq-question').forEach(question => {
    question.addEventListener('click', () => {
        const item = question.parentNode;
        item.classList.toggle('active');
    });
});
```

## Componentes de Rodapé

### 1. Rodapé com Informações de Contato e Redes Sociais

```html
<footer class="footer">
    <div class="container">
        <div class="footer-grid">
            <div class="footer-info">
                <h3>Nome da Empresa</h3>
                <p>Breve descrição da empresa e sua missão.</p>
                <div class="social-links">
                    <a href="#" class="social-link">FB</a>
                    <a href="#" class="social-link">IG</a>
                    <a href="#" class="social-link">TW</a>
                    <a href="#" class="social-link">YT</a>
                </div>
            </div>
            
            <div class="footer-info">
                <h3>Contato</h3>
                <p>📞 (00) 0000-0000</p>
                <p>✉️ <EMAIL></p>
                <p>📍 Endereço da empresa</p>
            </div>
            
            <div class="footer-info">
                <h3>Links Úteis</h3>
                <p><a href="#">Sobre Nós</a></p>
                <p><a href="#">Política de Privacidade</a></p>
                <p><a href="#">Termos de Uso</a></p>
            </div>
        </div>
        
        <div class="footer-bottom">
            <p>&copy; 2023 Nome da Empresa. Todos os direitos reservados.</p>
        </div>
    </div>
</footer>
```

```css
.footer {
    background-color: #000000;
    color: #ffffff;
    padding: 70px 0 30px;
    position: relative;
}

.footer:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(to right, #ffcc00, #e60000);
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 50px;
    margin-bottom: 40px;
}

.footer-info h3 {
    font-size: 1.5rem;
    margin-bottom: 25px;
    color: #ffcc00;
    font-weight: 700;
    position: relative;
    padding-bottom: 12px;
}

.footer-info h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #ffcc00;
}

.footer-info p {
    margin-bottom: 15px;
    font-size: 1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border-radius: 50%;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 1.3rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.7);
}
```

## Estilos Base Reutilizáveis

```css
/* Reset e estilos gerais */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Variáveis de cores (personalize conforme necessário) */
:root {
    --primary-color: #ffcc00;
    --secondary-color: #e60000;
    --dark-color: #000000;
    --light-color: #ffffff;
    --gray-color: #f5f5f5;
    --text-color: #333333;
}

/* Utilitários de espaçamento */
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 3rem; }

.py-1 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-2 { padding-top: 1rem; padding-bottom: 1rem; }
.py-3 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-4 { padding-top: 2rem; padding-bottom: 2rem; }
.py-5 { padding-top: 3rem; padding-bottom: 3rem; }

.px-1 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-2 { padding-left: 1rem; padding-right: 1rem; }
.px-3 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-4 { padding-left: 2rem; padding-right: 2rem; }
.px-5 { padding-left: 3rem; padding-right: 3rem; }
```
