/* Reset e estilos gerais */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}

.logo h1 {
    font-size: 24px;
    color: #0366d6;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

nav ul li a:hover {
    color: #0366d6;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #0366d6, #6e5494);
    color: white;
    padding: 150px 0 100px;
    text-align: center;
}

.hero-content h1 {
    font-size: 48px;
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 20px;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    background-color: #2ea44f;
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.cta-button:hover {
    background-color: #22863a;
}

/* Features Section */
.features {
    padding: 80px 0;
    background-color: #f6f8fa;
}

.features h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
}

.feature-cards {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.feature-cards .card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
    width: calc(33.333% - 20px);
    transition: transform 0.3s;
}

.feature-cards .card:hover {
    transform: translateY(-10px);
}

.feature-cards .card h3 {
    font-size: 24px;
    margin-bottom: 15px;
    color: #0366d6;
}

/* Pricing Section */
.pricing {
    padding: 80px 0;
}

.pricing h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
}

.pricing-cards {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.pricing-cards .card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
    width: calc(33.333% - 20px);
    text-align: center;
    transition: transform 0.3s;
}

.pricing-cards .card.featured {
    border: 2px solid #0366d6;
    transform: scale(1.05);
}

.pricing-cards .card:hover {
    transform: translateY(-10px);
}

.pricing-cards .card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-cards .card h3 {
    font-size: 24px;
    margin-bottom: 15px;
}

.pricing-cards .card .price {
    font-size: 48px;
    font-weight: bold;
    color: #0366d6;
    margin-bottom: 5px;
}

.pricing-cards .card ul {
    list-style: none;
    margin: 25px 0;
    text-align: left;
}

.pricing-cards .card ul li {
    margin-bottom: 10px;
    position: relative;
    padding-left: 25px;
}

.pricing-cards .card ul li:before {
    content: "✓";
    color: #2ea44f;
    position: absolute;
    left: 0;
}

/* Footer */
footer {
    background-color: #24292e;
    color: white;
    padding: 30px 0;
    text-align: center;
}

/* Responsividade */
@media (max-width: 768px) {
    .feature-cards .card,
    .pricing-cards .card {
        width: 100%;
    }
    
    .pricing-cards .card.featured {
        transform: none;
    }
    
    .pricing-cards .card.featured:hover {
        transform: translateY(-10px);
    }
    
    .hero-content h1 {
        font-size: 36px;
    }
    
    .hero-content p {
        font-size: 18px;
    }
}
