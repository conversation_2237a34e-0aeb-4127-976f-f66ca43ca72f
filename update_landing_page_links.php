<?php
/**
 * <PERSON>na <PERSON>e - Atualizador de Links da Landing Page
 * 
 * Este script atualiza os links dos produtos na landing page
 * com base nos IDs armazenados no banco de dados.
 */

// Configurações de conexão com o banco de dados
$config = [
    'host' => 'localhost',
    'dbname' => 'kina_restaurante',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4'
];

// Caminho para o arquivo HTML da landing page
$landingPagePath = 'kina-landing-page.html';

// Conexão com o banco de dados
try {
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ]);
} catch (PDOException $e) {
    die("Erro de conexão: " . $e->getMessage());
}

/**
 * Função para obter todos os produtos em destaque
 */
function getProdutosDestaque($pdo) {
    $stmt = $pdo->query("
        SELECT p.*, pu.ifood_id, pu.ifood_url
        FROM produtos p
        LEFT JOIN produtos_unidades pu ON p.id = pu.produto_id
        WHERE p.destaque = 1
        AND pu.unidade_id = (SELECT id FROM unidades WHERE nome = 'Kina Iguatemi')
        ORDER BY p.ordem_destaque ASC
    ");
    return $stmt->fetchAll();
}

/**
 * Função para obter a unidade principal
 */
function getUnidadePrincipal($pdo) {
    $stmt = $pdo->query("SELECT * FROM unidades WHERE nome = 'Kina Iguatemi' LIMIT 1");
    return $stmt->fetch();
}

/**
 * Função para atualizar os links na landing page
 */
function atualizarLinksLandingPage($landingPagePath, $produtos, $unidadePrincipal) {
    // Verificar se o arquivo existe
    if (!file_exists($landingPagePath)) {
        return "Arquivo da landing page não encontrado: $landingPagePath";
    }
    
    // Ler o conteúdo do arquivo
    $html = file_get_contents($landingPagePath);
    
    // Verificar se conseguiu ler o arquivo
    if ($html === false) {
        return "Não foi possível ler o arquivo da landing page.";
    }
    
    // Criar um backup do arquivo original
    $backupPath = $landingPagePath . '.bak.' . date('YmdHis');
    if (!copy($landingPagePath, $backupPath)) {
        return "Não foi possível criar um backup do arquivo.";
    }
    
    // URL base da unidade principal
    $baseUrl = $unidadePrincipal['ifood_url'];
    
    // Para cada produto, atualizar o link correspondente
    foreach ($produtos as $produto) {
        $nomeNormalizado = strtolower(
            preg_replace('/[^a-zA-Z0-9]/', '-', 
            iconv('UTF-8', 'ASCII//TRANSLIT', $produto['nome'])
        ));
        
        // Padrão para encontrar o link do produto
        $pattern = '/<a href="[^"]*\?prato=' . preg_quote($nomeNormalizado, '/') . '"[^>]*>/i';
        
        // Novo link com o ID real do iFood
        if (!empty($produto['ifood_url'])) {
            $novoLink = '<a href="' . $produto['ifood_url'] . '" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>';
        } else if (!empty($produto['ifood_id'])) {
            $novoLink = '<a href="' . $baseUrl . '?prato=' . $produto['ifood_id'] . '" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>';
        } else {
            // Se não tiver ID, manter o link para a página principal do restaurante
            $novoLink = '<a href="' . $baseUrl . '" target="_blank" class="btn-cta btn-cta-secondary" style="padding: 8px 15px; font-size: 0.8rem;">Pedir</a>';
        }
        
        // Substituir o link no HTML
        $html = preg_replace($pattern, $novoLink, $html);
    }
    
    // Escrever o HTML atualizado de volta para o arquivo
    if (file_put_contents($landingPagePath, $html) === false) {
        return "Não foi possível escrever no arquivo da landing page.";
    }
    
    return "Links atualizados com sucesso! Backup criado em: $backupPath";
}

// Obter os produtos em destaque e a unidade principal
$produtosDestaque = getProdutosDestaque($pdo);
$unidadePrincipal = getUnidadePrincipal($pdo);

// Atualizar os links na landing page
$resultado = atualizarLinksLandingPage($landingPagePath, $produtosDestaque, $unidadePrincipal);

// Exibir o resultado
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Atualizador de Links - Kina Restaurante</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #E60000;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .alert {
            padding: 15px;
            background-color: #d4edda;
            color: #155724;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #FFCC00;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .btn {
            display: inline-block;
            background-color: #E60000;
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .btn:hover {
            background-color: #cc0000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Atualizador de Links - Kina Restaurante</h1>
        
        <div class="<?php echo strpos($resultado, 'sucesso') !== false ? 'alert' : 'alert alert-error'; ?>">
            <?php echo $resultado; ?>
        </div>
        
        <h2>Produtos em Destaque</h2>
        <?php if (empty($produtosDestaque)): ?>
            <p>Nenhum produto em destaque encontrado no banco de dados.</p>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>Nome</th>
                        <th>ID no iFood</th>
                        <th>URL no iFood</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($produtosDestaque as $produto): ?>
                        <tr>
                            <td><?php echo $produto['nome']; ?></td>
                            <td><?php echo $produto['ifood_id'] ?: 'Não definido'; ?></td>
                            <td>
                                <?php if (!empty($produto['ifood_url'])): ?>
                                    <a href="<?php echo $produto['ifood_url']; ?>" target="_blank">
                                        <?php echo $produto['ifood_url']; ?>
                                    </a>
                                <?php else: ?>
                                    Não definido
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
        
        <a href="kina_product_manager.php" class="btn">Gerenciar Produtos</a>
        <a href="<?php echo $landingPagePath; ?>" class="btn" target="_blank">Ver Landing Page</a>
    </div>
</body>
</html>
